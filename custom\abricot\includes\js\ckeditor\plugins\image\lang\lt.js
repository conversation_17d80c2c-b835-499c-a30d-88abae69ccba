﻿/*
Copyright (c) 2003-2013, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'lt', {
	alertUrl: 'Prašome įvesti vaizdo URL',
	alt: 'Alternatyvus Tekstas',
	border: '<PERSON>ė<PERSON><PERSON>',
	btnUpload: 'Siųsti į serverį',
	button2Img: 'Ar norite mygtuką paversti paprastu paveiksliuku?',
	hSpace: 'Hor.Erdvė',
	img2Button: 'Ar norite paveiksliuką paversti mygtuku?',
	infoTab: 'Vaizdo informacija',
	linkTab: 'Nuoroda',
	lockRatio: 'I<PERSON><PERSON><PERSON>ti proporciją',
	menu: 'Vaizdo savybės',
	resetSize: 'Atstatyti dydį',
	title: '<PERSON>ai<PERSON><PERSON> savybė<PERSON>',
	titleButton: '<PERSON>ai<PERSON><PERSON>io mygtuko savybė<PERSON>',
	upload: 'Nusiųsti',
	urlMissing: 'Paveiksliuk<PERSON> nuorod<PERSON> nėra.',
	vSpace: 'Vert.Erdvė',
	validateBorder: '<PERSON><PERSON><PERSON><PERSON><PERSON> turi būti sveikas skaičius.',
	validateHSpace: 'Reikšmė turi būti sveikas skaičius.',
	validateVSpace: 'Reikšmė turi būti sveikas skaičius.'
});
