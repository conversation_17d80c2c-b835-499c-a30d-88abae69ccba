{"name": "jt.timepicker", "version": "1.2.16", "title": "j<PERSON>y-timepicker", "description": "A jQuery timepicker plugin inspired by Google Calendar. It supports both mouse and keyboard navigation.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/jont<PERSON>ton"}, "licenses": [{"type": "MIT", "url": "MIT-LICENSE.txt"}], "dependencies": {"jquery": ">=1.7"}, "keywords": ["timepicker", "time", "picker", "ui", "calendar", "input", "form"], "homepage": "http://jonthornton.github.com/jquery-timepicker/", "bugs": {"url": "https://github.com/jonthornton/jquery-timepicker/issues"}, "docs": "https://github.com/jonthornton/jquery-timepicker", "download": "https://github.com/jonthornton/jquery-timepicker"}