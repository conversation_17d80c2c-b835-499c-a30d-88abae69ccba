﻿/*
Copyright (c) 2003-2013, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'zh-cn', {
	alertUrl: '请输入图象地址',
	alt: '替换文本',
	border: '边框大小',
	btnUpload: '上传到服务器上',
	button2Img: '确定要把当前按钮改变为图像吗?',
	hSpace: '水平间距',
	img2Button: '确定要把当前图像改变为按钮吗?',
	infoTab: '图象',
	linkTab: '链接',
	lockRatio: '锁定比例',
	menu: '图象属性',
	resetSize: '原始尺寸',
	title: '图象属性',
	titleButton: '图像域属性',
	upload: '上传',
	urlMissing: '缺少图像源文件地址',
	vSpace: '垂直间距',
	validateBorder: '边框大小必须为整数格式',
	validateHSpace: '水平间距必须为整数格式',
	validateVSpace: '垂直间距必须为整数格式'
});
