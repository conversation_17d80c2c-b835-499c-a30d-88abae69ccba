﻿/*
Copyright (c) 2003-2013, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'pt', {
	alertUrl: 'Por favor introduza o URL da imagem',
	alt: 'Texto Alternativo',
	border: 'Limite',
	btnUpload: 'Enviar para o Servidor',
	button2Img: 'Do you want to transform the selected image button on a simple image?', // MISSING
	hSpace: 'Esp.Horiz',
	img2Button: 'Do you want to transform the selected image on a image button?', // MISSING
	infoTab: 'Informação da Imagem',
	linkTab: 'Hiperligação',
	lockRatio: 'Proporcional',
	menu: 'Propriedades da Imagem',
	resetSize: 'Tamanho Original',
	title: 'Propriedades da Imagem',
	titleButton: 'Propriedades do Botão de imagens',
	upload: 'Carregar',
	urlMissing: 'Image source URL is missing.', // MISSING
	vSpace: 'Esp.Vert',
	validateBorder: 'Border must be a whole number.', // MISSING
	validateHSpace: 'HSpace must be a whole number.', // MISSING
	validateVSpace: 'VSpace must be a whole number.' // MISSING
});
