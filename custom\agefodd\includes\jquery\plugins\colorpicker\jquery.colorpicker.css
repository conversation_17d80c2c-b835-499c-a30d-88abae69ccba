.colorpicker-picker-span{
    display: block;
    width: 20px;
    height: 20px;
    float: left;
    border: 1px solid #000;
    margin-right: 2px;
    margin-bottom: 4px;
    cursor: pointer;
}

.colorpicker-picker-info{
    padding: 0 0 2px;
    color: #000;
    text-align: center;
    text-transform: uppercase;
}

.colorpicker-picker-span:hover {
    border: 1px solid #000;
}

.colorpicker-picker-span.active {
    border: 1px solid #000;
}

.colorpicker-picker {
    background-color: #EEEEEE;
    padding: 5px;
    display: none;
    position: absolute;
    top: 0;
    -moz-border-radius: 5px;
    border-radius: 5px;
    border: 1px solid #CCCCCC;
    box-shadow: 2px 2px 5px #111;
    -moz-box-shadow: 2px 2px 5px #111;
    -webkit-box-shadow: 2px 2px 5px #111;
}

.colorpicker-trigger {
    display: block;
    width: 15px;
    height: 15px;
    border: 1px solid #000;
    cursor: pointer;
    background-color: #ffffff;
}


.colorpicker-wrap {
    font-family: 'Trebuchet MS', Verdana, Arial, Geneva, sans-serif
}

.colorpicker-label {
    float: left;
    margin-right: 2px;
}