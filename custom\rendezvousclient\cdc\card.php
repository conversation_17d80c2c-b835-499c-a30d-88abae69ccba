<?php
/**
 *	\file       rendezvousclient/cdc/card.php
 *	\ingroup    rendezvousclient
 *	\brief      fiche cahier des charges
 */

require_once '../../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

require_once DOL_DOCUMENT_ROOT.'/societe/class/societe.class.php';
dol_include_once('/rendezvousclient/lib/rendezvousclient.lib.php');
dol_include_once('/rendezvousclient/cdc/class/cdc.class.php');
require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.formother.class.php';
require_once DOL_DOCUMENT_ROOT.'/projet/class/project.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/doleditor.class.php';
require_once DOL_DOCUMENT_ROOT.'/contact/class/contact.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/files.lib.php';

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'users', 'companies', 'propal'));

// id de la table rendez_vous_cahier_des_charges
$id = GETPOST('id', 'int');
$action = GETPOST('action', 'aZ09');
$cancel	= GETPOST('cancel', 'alphanohtml');
$backtopage = GETPOST('backtopage', 'alpha');
$help_url = '';

// charge le cahier des charges $id
$cdc = new CahierDesCharges($db);
if($id > 0){
    $cdc->fetch($id);
}

$projectstatic = new Project($db);
if($cdc->fk_projet > 0){
    $projectstatic->fetch($cdc->fk_projet);
    $cdc->socid = $projectstatic->socid;
}

$societe = new Societe($db);
if($projectstatic->socid > 0){
    $societe->fetch($projectstatic->socid);
}


/*
 * Actions
*/


if($action == 'add' && GETPOST('save') == $langs->trans('Save')){
    var_dump($_POST);

    exit;
}

if($action == 'update' && GETPOST('save') == $langs->trans('Save')){
    var_dump($_POST);

    exit;
}


/*
 * View
*/

$form = new Form($db);
$formother = new FormOther($db);
$contactstatic = new Contact($db);

$title = $langs->trans('Cdc');

llxHeader('', $title, $help_url);

if($action == 'create'){
    /*
     *  Creation
    */

    if(GETPOST('socid', 'int')){
        $societe->fetch(GETPOST('socid', 'int'));
    }

    $linkback = "";
    print load_fiche_titre($langs->trans("Cdc"), $linkback, 'generic');

    print '<form enctype="multipart/form-data" action="'.$_SERVER["PHP_SELF"].'" method="post" name="formcdc" autocomplete="off">'; // Chrome ignor autocomplete
    print '<input type="hidden" name="action" value="add">';
    print '<input type="hidden" name="token" value="'.newToken().'">';
    print '<input type="hidden" name="backtopage" value="'.$backtopage.'">';
    print dol_get_fiche_head(null, 'card', '', 0, '');

    print '<table class="border centpercent">';

    // Thirdparty
	print '<tr>';
	print '<td class="fieldrequired">'.$langs->trans('Customer').'</td>';
	if ($societe->id > 0) {
		if(!empty($conf->global->MAIN_AVIMM_CANCHANGESOC)){
			print '<td>';
			print img_picto('', 'company').$form->select_company($societe->id, 'socid', '', 'SelectThirdParty', 0, 0, null, 0, 'minwidth300 maxwidth500 widthcentpercentminusxx');
			// reload page to retrieve customer informations
			if (empty($conf->global->RELOAD_PAGE_ON_CUSTOMER_CHANGE_DISABLED)) {
				print '<script type="text/javascript">
				$(document).ready(function() {
					$("#socid").change(function() {
						console.log("We have changed the company - Reload page");
						var socid = $(this).val();
						// reload page
						$("input[name=action]").val("create");
						$("form[name=formcdc]").submit();
					});
				});
				</script>';
			}
			print '</td>';
		}else{
			print '<td>';
			print $societe->getNomUrl(1, 'customer');
			print '<input type="hidden" name="socid" value="'.$societe->id.'">';
			print '</td>';
		}
	} else {
		print '<td>';
		print img_picto('', 'company').$form->select_company('', 'socid', '', 'SelectThirdParty', 0, 0, null, 0, 'minwidth175 maxwidth500 widthcentpercentminusxx');
		// reload page to retrieve customer informations
		if (empty($conf->global->RELOAD_PAGE_ON_CUSTOMER_CHANGE_DISABLED)) {
			print '<script type="text/javascript">
			$(document).ready(function() {
				$("#socid").change(function() {
					console.log("We have changed the company - Reload page");
					var socid = $(this).val();
					// reload page
					$("input[name=action]").val("create");
					$("form[name=formcdc]").submit();
				});
			});
			</script>';
		}
		print '</td>';
	}
	print '</tr>';

    if($societe->id){
        // projet
        $sql = "SELECT rowid, ref, title";
        $sql .= " FROM ".MAIN_DB_PREFIX."projet";
        $sql .= " WHERE fk_soc = ".$societe->id;
        $resql = $db->query($sql);

        print '<tr>';
        print '<td class="fieldrequired">'.$langs->trans('Project').'</td>';
        if($resql && $db->num_rows($resql) > 0){
            print '<td>';
            $arrayprojet = array();
            while($obj = $db->fetch_object($resql)){
                $arrayprojet[$obj->rowid] = $obj->ref.' - '.$obj->title;
            }
            print $form->selectarray('fk_projet', $arrayprojet, (GETPOST('fk_projet', 'int') ? GETPOST('fk_projet', 'int') : ''), 1);
            print '<script type="text/javascript">
			$(document).ready(function() {
				$("#fk_projet").change(function() {
					console.log("We have changed the company - Reload page");
					var fk_projet = $(this).val();
					// reload page
					$("input[name=action]").val("create");
					$("form[name=formcdc]").submit();
				});
			});
			</script>';
            print '</td>';
        }else{
            print '<td>Aucun projet pour ce client';
            print ' <a href="'.DOL_URL_ROOT.'/projet/card.php?action=create&socid='.$societe->id.'&backtopage='.urlencode($_SERVER["PHP_SELF"].'?action=create').'"><span class="fa fa-plus-circle valignmiddle paddingleft" title="'.$langs->trans("AddThirdParty").'"></span></a>';
            print '</td>';
        }
        print '</tr>';
    }

    if(GETPOST('fk_projet',' int')){
        $sql = "SELECT rowid, version";
        $sql .= " FROM ".MAIN_DB_PREFIX."rendez_vous_cahier_des_charges";
        $sql .= " WHERE fk_projet = ".GETPOST('fk_projet', 'int')." ORDER BY rowid DESC LIMIT 1";
        $resql = $db->query($sql);

        $version = 'V1'; // Version par défaut
        if($resql && $db->num_rows($resql) > 0){
            $obj = $db->fetch_object($resql);
            if($obj && $obj->version){
                $version_num = substr($obj->version, 1);
                $version = 'V'.($version_num+1);
            }
        }

        print '<tr>';
        print '<td>'.$langs->trans("version_num").'</td>';
        print '<td><input type="text" value="'.$version.'" disabled><input type="hidden" name="version" value="'.$version.'"></td>';
        print '</tr>';

        print '<tr>';
        print '<td style="width: 30%;">'.$langs->trans("DateD").'</td>';
        $date_demo = dol_now();
        if(GETPOST('date_livraison')){
            $date_demo = dol_mktime(GETPOST('date_demohour', 'int'), GETPOST('date_demomin', 'int'), 0, GETPOST('date_demomonth', 'int'), GETPOST('date_demoday', 'int'), GETPOST('date_demoyear', 'int'));
        }
        print '<td>'.$form->selectDate($date_demo, 'date_demo', 1, 1, '', "formcdc", 1, 0, 0, '', '', '', '', 1, '', '', 'auto').'</td>';
        print '</tr>';

        print '<tr>';
        print '<td style="width: 30%;">'.$langs->trans("DATE_LIVRAISON").'</td>';
        $date_livraison = dol_now();
        if(GETPOST('date_livraison')){
            $date_livraison = dol_mktime(GETPOST('date_livraisonhour', 'int'), GETPOST('date_livraisonmin', 'int'), 0, GETPOST('date_livraisonmonth', 'int'), GETPOST('date_livraisonday', 'int'), GETPOST('date_livraisonyear', 'int'));
        }
        print '<td>'.$form->selectDate($date_livraison, 'date_livraison', 1, 1, '', "formcdc", 1, 0, 0, '', '', '', '', 1, '', '', 'auto').'</td>';
        print '</tr>';

        print '<tr>';
        print '<td style="width: 30%;">'.$langs->trans("nb_rdv_amont").'</td>';
        print '<td><input type="text" maxlength="100" name="nb_rdv_amont" value="'.GETPOST('nb_rdv_amont', 'alphanohtml').'"></td>';
        print '</tr>';

        print '<tr>';
        print '<td style="width: 30%;">'.$langs->trans("nb_rdv_aval").'</td>';
        print '<td><input type="text" maxlength="100" name="nb_rdv_aval" value="'.GETPOST('nb_rdv_aval', 'alphanohtml').'"></td>';
        print '</tr>';

        print '<tr>';
        print '<td style="width: 30%;">'.$langs->trans("nb_jours_formations").'</td>';
        print '<td><input type="text" maxlength="100" name="nb_jours_formations" value="'.GETPOST('nb_jours_formations', 'alphanohtml').'"></td>';
        print '</tr>';

        print '<tr>';
        print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>I. '.$langs->trans("Introduction").'<br/><br/></td>';
        print '</tr>';
        print '<tr>';
        print '<td style="text-align: center;">1.1 '.$langs->trans("intro_contexte_projet").'</td>';
        print '<td>';
        $doleditor = new DolEditor('intro_contexte_projet', GETPOST('intro_contexte_projet', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<td style="text-align: center;">1.2 '.$langs->trans("intro_objectifs_globaux").'</td>';
        print '<td>';
        $doleditor = new DolEditor('intro_objectifs_globaux', GETPOST('intro_objectifs_globaux', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<tr>';
        print '<td style="text-align: center;">1.3 '.$langs->trans("intro_presentation_client").'</td>';
        print '<td>';
        $doleditor = new DolEditor('intro_presentation_client', GETPOST('intro_presentation_client', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';

        print '<tr>';
        print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>II. '.$langs->trans("Perimetre_projet").'<br/><br/></td>';
        print '</tr>';
        print '<tr>';
        print '<td style="text-align: center;">2.1 '.$langs->trans("perimetre_projet_delimitation").'</td>';
        print '<td>';
        $doleditor = new DolEditor('perimetre_projet_delimitation', GETPOST('perimetre_projet_delimitation', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<tr>';
        print '<td style="text-align: center;">2.2 '.$langs->trans("perimetre_projet_def_processus").'</td>';
        print '<td>';
        $doleditor = new DolEditor('perimetre_projet_def_processus', GETPOST('perimetre_projet_def_processus', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';

        print '<tr>';
        print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>III. '.$langs->trans("Besoins_fonctionnels").'<br/><br/></td>';
        print '<tr>';
        print '<td style="text-align: center;">3.1 '.$langs->trans("besoins_processus_par_site").'</td>';
        print '<td>';
        $doleditor = new DolEditor('besoins_processus_par_site', GETPOST('besoins_processus_par_site', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<tr>';
        print '<td style="text-align: center;">3.2 '.$langs->trans("besoins_fonctionnels_user").'</td>';
        print '<td>';
        $doleditor = new DolEditor('besoins_fonctionnels_user', GETPOST('besoins_fonctionnels_user', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';

        print '<tr>';
        print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>IV. '.$langs->trans("Architecture_tech_logicielle").'<br/><br/></td>';
        print '</tr>';
        print '<tr>';
        print '<td style="text-align: center;">4.1 '.$langs->trans("architecture_solutions").'</td>';
        print '<td>';
        $doleditor = new DolEditor('architecture_solutions', GETPOST('architecture_solutions', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<tr>';
        print '<td style="text-align: center;">4.2 '.$langs->trans("architecture_modules").'</td>';
        print '<td>';
        $doleditor = new DolEditor('architecture_modules', GETPOST('architecture_modules', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<tr>';
        print '<td style="text-align: center;">4.3 '.$langs->trans("architecture_infrastructure").'</td>';
        print '<td>';
        $doleditor = new DolEditor('architecture_infrastructure', GETPOST('architecture_infrastructure', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';

        print '<tr>';
        print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>V. '.$langs->trans("Gestion_info_process").'<br/><br/></td>';
        print '</tr>';
        print '<tr>';
        print '<td style="text-align: center;">5.1 '.$langs->trans("process_av_vente").'</td>';
        print '<td>';
        $doleditor = new DolEditor('process_av_vente', GETPOST('process_av_vente', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<tr>';
        print '<td style="text-align: center;">5.2 '.$langs->trans("flux_logistiques").'</td>';
        print '<td>';
        $doleditor = new DolEditor('flux_logistiques', GETPOST('flux_logistiques', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<tr>';
        print '<td style="text-align: center;">5.3 '.$langs->trans("prod_et_qualite").'</td>';
        print '<td>';
        $doleditor = new DolEditor('prod_et_qualite', GETPOST('prod_et_qualite', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<tr>';
        print '<td style="text-align: center;">5.4 '.$langs->trans("commandes_et_expeditions").'</td>';
        print '<td>';
        $doleditor = new DolEditor('commandes_et_expeditions', GETPOST('commandes_et_expeditions', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<tr>';
        print '<td style="text-align: center;">5.5 '.$langs->trans("creation_recettes").'</td>';
        print '<td>';
        $doleditor = new DolEditor('creation_recettes', GETPOST('creation_recettes', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<tr>';
        print '<td style="text-align: center;">5.6 '.$langs->trans("reporting_analyse").'</td>';
        print '<td>';
        $doleditor = new DolEditor('reporting_analyse', GETPOST('reporting_analyse', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';

        print '<tr>';
        print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>VI. '.$langs->trans("Interface_user_experience").'<br/><br/></td>';
        print '</tr>';
        print '<tr>';
        print '<td style="text-align: center;">6.1 '.$langs->trans("ux_roles").'</td>';
        print '<td>';
        $doleditor = new DolEditor('ux_roles', GETPOST('ux_roles', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<tr>';
        print '<td style="text-align: center;">6.2 '.$langs->trans("ux_interfaces").'</td>';
        print '<td>';
        $doleditor = new DolEditor('ux_interfaces', GETPOST('ux_interfaces', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<tr>';
        print '<td style="text-align: center;">6.3 '.$langs->trans("ux_kpi_client").'</td>';
        print '<td>';
        $doleditor = new DolEditor('ux_kpi_client', GETPOST('ux_kpi_client', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';

        print '<tr>';
        print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>VII. '.$langs->trans("Formation_docu").'<br/><br/></td>';
        print '</tr>';
        print '<tr>';
        print '<td style="text-align: center;">7.1 '.$langs->trans("formation_programme").'</td>';
        print '<td>';
        $doleditor = new DolEditor('formation_programme', GETPOST('formation_programme', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<tr>';
        print '<td style="text-align: center;">7.2 '.$langs->trans("formation_documentation").'</td>';
        print '<td>';
        $doleditor = new DolEditor('formation_documentation', GETPOST('formation_documentation', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';

        print '<tr>';
        print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>VIII. '.$langs->trans("Maintenace_support").'<br/><br/></td>';
        print '</tr>';
        print '<tr>';
        print '<td style="text-align: center;">8.1 '.$langs->trans("maintenance_support").'</td>';
        print '<td>';
        $doleditor = new DolEditor('maintenance_support', GETPOST('maintenance_support', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<tr>';
        print '<td style="text-align: center;">8.2 '.$langs->trans("maintenance_mises_a_jour").'</td>';
        print '<td>';
        $doleditor = new DolEditor('maintenance_mises_a_jour', GETPOST('maintenance_mises_a_jour', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';

        print '<tr>';
        print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>IX. '.$langs->trans("Deploiement_planning").'<br/><br/></td>';
        print '</tr>';
        print '<tr>';
        print '<td style="text-align: center;">9.1 '.$langs->trans("deploiement_calendrier").'</td>';
        print '<td>';
        $doleditor = new DolEditor('deploiement_calendrier', GETPOST('deploiement_calendrier', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<tr>';
        print '<td style="text-align: center;">9.2 '.$langs->trans("deploiement_jalons").'</td>';
        print '<td>';
        $doleditor = new DolEditor('deploiement_jalons', GETPOST('deploiement_jalons', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';

        print '<tr>';
        print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>X. '.$langs->trans("Critere_reussite").'<br/><br/></td>';
        print '</tr>';
        print '<tr>';
        print '<td style="text-align: center;">10.1 '.$langs->trans("succes_kpi").'</td>';
        print '<td>';
        $doleditor = new DolEditor('succes_kpi', GETPOST('succes_kpi', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<tr>';
        print '<td style="text-align: center;">10.2 '.$langs->trans("succes_suivi").'</td>';
        print '<td>';
        $doleditor = new DolEditor('succes_suivi', GETPOST('succes_suivi', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';

        print '<tr>';
        print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>XI. '.$langs->trans("budget_previsionnel").'<br/><br/></td>';
        print '</tr>';
        print '<tr>';
        print '<td></td>';
        print '<td>';
        $doleditor = new DolEditor('budget_previsionnel', GETPOST('budget_previsionnel', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';

        print '<tr>';
        print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>XII. '.$langs->trans("modifs_recommandations").'<br/><br/></td>';
        print '</tr>';
        print '<tr>';
        print '<td></td>';
        print '<td>';
        $doleditor = new DolEditor('modifs_recommandations', GETPOST('modifs_recommandations', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';

        print '<tr>';
        print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>XIII. '.$langs->trans("Mentions_legal_validation").'<br/><br/></td>';
        print '</tr>';
        print '<tr>';
        print '<td style="text-align: center;">13.1 '.$langs->trans("mentions_confidentialite").'</td>';
        print '<td>';
        $doleditor = new DolEditor('mentions_confidentialite', GETPOST('mentions_confidentialite', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<tr>';
        print '<td style="text-align: center;">13.2 '.$langs->trans("mentions_propriete").'</td>';
        print '<td>';
        $doleditor = new DolEditor('mentions_propriete', GETPOST('mentions_propriete', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<tr>';
        print '<td style="text-align: center;">13.3 '.$langs->trans("mentions_conditions_modification").'</td>';
        print '<td>';
        $doleditor = new DolEditor('mentions_conditions_modification', GETPOST('mentions_conditions_modification', 'restricthtml'), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
        print $doleditor->Create(1);
        print '</td></tr>';
        print '<tr>';
    }

    print '</table>';

    print dol_get_fiche_end();

    print $form->buttonsSaveCancel("Save");

	print '</form>';


}elseif($action == 'edit'){
    /*
     *  Edition
    */
    $head = cdc_prepare_head($cdc);

    print dol_get_fiche_head($head, 'card', $langs->trans("Cdc"), -1, 'generic');

    print '
    <div class="arearef heightref valignmiddle centpercent">
        <!-- Start banner content -->
        <div style="vertical-align: middle">
            <div class="pagination paginationref">
                <ul class="right">
                    <li class="noborder litext clearbothonsmartphone">
                        <a href="'.DOL_URL_ROOT.'/custom/rendezvousclient/cdc/list.php">Retour liste</a>
                    </li>
                </ul>
            </div>
            <!-- morehtmlleft -->
            <div class="inline-block floatleft">
                <!-- No photo to show -->
                <div class="floatleft inline-block valignmiddle divphotoref">
                    <div class="photoref">
                        <!-- <span class="fas fa-project-diagram  em088 infobox-project" style="" title="No photo"></span> -->
                        '.img_picto('', 'action').'
                    </div>
                </div>
            </div>
            <div class="inline-block floatleft valignmiddle maxwidth750 marginbottomonly refid refidpadding">
                <div class="refidno">
                    ';
                    print $langs->trans('ThirdParty').' : '.$societe->getNomUrl(1, '');

                    print '<br/>'.$langs->trans('Phone').' : '.dol_print_phone($societe->phone, $societe->country_code, $contactid, $societe->id, 'AC_TEL', '&nbsp;', 'phone', $langs->trans("Phone"));

                    print '<br/>'.$langs->trans('Email').' : '.dol_print_email($societe->email, $societe->id, $societe->id, 'AC_EMAIL', 0, 0, 1);

                    print '<br/>'.$langs->trans('Project').' : '. $projectstatic->ref;
                    print '
                </div>
            </div>
        </div>
        <!-- End banner content -->
    </div>';
    print '<div class="underbanner clearboth"></div><br/>';

    print '<form enctype="multipart/form-data" action="'.$_SERVER["PHP_SELF"].'" method="post" name="formcdc" autocomplete="off">'; // Chrome ignor autocomplete
    print '<input type="hidden" name="action" value="update">';
    print '<input type="hidden" name="token" value="'.newToken().'">';
    print '<input type="hidden" name="id" value="'.$id.'">';

    print '<table class="border centpercent">';

    print '<tr class="titlefieldcreate">';
    print '<td>'.$langs->trans("version_num").'</td>';
    print '<td><input type="text" maxlength="100" name="version" value="'.$cdc->version.'"></td>';
    print '</tr>';

    print '<tr>';
    print '<td style="width: 30%;">'.$langs->trans("DateD").'</td>';
    $date_demo = ($cdc->date_demo ? strtotime($cdc->date_demo) : '');
    if(GETPOST('date_livraison')){
        $date_demo = dol_mktime(GETPOST('date_demohour', 'int'), GETPOST('date_demomin', 'int'), 0, GETPOST('date_demomonth', 'int'), GETPOST('date_demoday', 'int'), GETPOST('date_demoyear', 'int'));
    }
    print '<td>'.$form->selectDate($date_demo, 'date_demo', 1, 1, '', "formcdc", 1, 0, 0, '', '', '', '', 1, '', '', 'auto').'</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="width: 30%;">'.$langs->trans("DATE_LIVRAISON").'</td>';
    $date_livraison = ($cdc->date_livraison ? strtotime($cdc->date_livraison) : '');
    if(GETPOST('date_livraison')){
        $date_livraison = dol_mktime(GETPOST('date_livraisonhour', 'int'), GETPOST('date_livraisonmin', 'int'), 0, GETPOST('date_livraisonmonth', 'int'), GETPOST('date_livraisonday', 'int'), GETPOST('date_livraisonyear', 'int'));
    }
    print '<td>'.$form->selectDate($date_livraison, 'date_livraison', 1, 1, '', "formcdc", 1, 0, 0, '', '', '', '', 1, '', '', 'auto').'</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="width: 30%;">'.$langs->trans("nb_rdv_amont").'</td>';
    print '<td><input type="text" maxlength="100" name="nb_rdv_amont" value="'.(GETPOST('nb_rdv_amont', 'alphanohtml') ? GETPOST('nb_rdv_amont', 'alphanohtml') : $cdc->nb_rdv_amont).'"></td>';
    print '</tr>';

    print '<tr>';
    print '<td style="width: 30%;">'.$langs->trans("nb_rdv_aval").'</td>';
    print '<td><input type="text" maxlength="100" name="nb_rdv_aval" value="'.(GETPOST('nb_rdv_aval', 'alphanohtml') ? GETPOST('nb_rdv_aval', 'alphanohtml') : $cdc->nb_rdv_aval).'"></td>';
    print '</tr>';

    print '<tr>';
    print '<td style="width: 30%;">'.$langs->trans("nb_jours_formations").'</td>';
    print '<td><input type="text" maxlength="100" name="nb_jours_formations" value="'.(GETPOST('nb_jours_formations', 'alphanohtml') ? GETPOST('nb_jours_formations', 'alphanohtml') : $cdc->nb_jours_formations).'"></td>';
    print '</tr>';

    print '<tr>';
    print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>I. '.$langs->trans("Introduction").'<br/><br/></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="text-align: center;">1.1 '.$langs->trans("intro_contexte_projet").'</td>';
	print '<td>';
	$doleditor = new DolEditor('intro_contexte_projet', (GETPOST('intro_contexte_projet', 'restricthtml') ? GETPOST('intro_contexte_projet', 'restricthtml') : $cdc->intro_contexte_projet), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<td style="text-align: center;">1.2 '.$langs->trans("intro_objectifs_globaux").'</td>';
    print '<td>';
	$doleditor = new DolEditor('intro_objectifs_globaux', (GETPOST('intro_objectifs_globaux', 'restricthtml') ? GETPOST('intro_objectifs_globaux', 'restricthtml') : $cdc->intro_objectifs_globaux), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<tr>';
    print '<td style="text-align: center;">1.3 '.$langs->trans("intro_presentation_client").'</td>';
    print '<td>';
	$doleditor = new DolEditor('intro_presentation_client', (GETPOST('intro_presentation_client', 'restricthtml') ? GETPOST('intro_presentation_client', 'restricthtml') : $cdc->intro_presentation_client), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    print '<tr>';
    print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>II. '.$langs->trans("Perimetre_projet").'<br/><br/></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="text-align: center;">2.1 '.$langs->trans("perimetre_projet_delimitation").'</td>';
    print '<td>';
	$doleditor = new DolEditor('perimetre_projet_delimitation', (GETPOST('perimetre_projet_delimitation', 'restricthtml') ? GETPOST('perimetre_projet_delimitation', 'restricthtml') : $cdc->perimetre_projet_delimitation), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<tr>';
    print '<td style="text-align: center;">2.2 '.$langs->trans("perimetre_projet_def_processus").'</td>';
    print '<td>';
	$doleditor = new DolEditor('perimetre_projet_def_processus', (GETPOST('perimetre_projet_def_processus', 'restricthtml') ? GETPOST('perimetre_projet_def_processus', 'restricthtml') : $cdc->perimetre_projet_def_processus), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    print '<tr>';
    print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>III. '.$langs->trans("Besoins_fonctionnels").'<br/><br/></td>';
    print '<tr>';
    print '<td style="text-align: center;">3.1 '.$langs->trans("besoins_processus_par_site").'</td>';
    print '<td>';
	$doleditor = new DolEditor('besoins_processus_par_site', (GETPOST('besoins_processus_par_site', 'restricthtml') ? GETPOST('besoins_processus_par_site', 'restricthtml') : $cdc->besoins_processus_par_site), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<tr>';
    print '<td style="text-align: center;">3.2 '.$langs->trans("besoins_fonctionnels_user").'</td>';
    print '<td>';
	$doleditor = new DolEditor('besoins_fonctionnels_user', (GETPOST('besoins_fonctionnels_user', 'restricthtml') ? GETPOST('besoins_fonctionnels_user', 'restricthtml') : $cdc->besoins_fonctionnels_user), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    print '<tr>';
    print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>IV. '.$langs->trans("Architecture_tech_logicielle").'<br/><br/></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="text-align: center;">4.1 '.$langs->trans("architecture_solutions").'</td>';
    print '<td>';
    $doleditor = new DolEditor('architecture_solutions', (GETPOST('architecture_solutions', 'restricthtml') ? GETPOST('architecture_solutions', 'restricthtml') : $cdc->architecture_solutions), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<tr>';
    print '<td style="text-align: center;">4.2 '.$langs->trans("architecture_modules").'</td>';
    print '<td>';
	$doleditor = new DolEditor('architecture_modules', (GETPOST('architecture_modules', 'restricthtml') ? GETPOST('architecture_modules', 'restricthtml') : $cdc->architecture_modules), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<tr>';
    print '<td style="text-align: center;">4.3 '.$langs->trans("architecture_infrastructure").'</td>';
    print '<td>';
	$doleditor = new DolEditor('architecture_infrastructure', (GETPOST('architecture_infrastructure', 'restricthtml') ? GETPOST('architecture_infrastructure', 'restricthtml') : $cdc->architecture_infrastructure), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    print '<tr>';
    print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>V. '.$langs->trans("Gestion_info_process").'<br/><br/></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="text-align: center;">5.1 '.$langs->trans("process_av_vente").'</td>';
    print '<td>';
    $doleditor = new DolEditor('process_av_vente', (GETPOST('process_av_vente', 'restricthtml') ? GETPOST('process_av_vente', 'restricthtml') : $cdc->process_av_vente), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<tr>';
    print '<td style="text-align: center;">5.2 '.$langs->trans("flux_logistiques").'</td>';
    print '<td>';
    $doleditor = new DolEditor('flux_logistiques', (GETPOST('flux_logistiques', 'restricthtml') ? GETPOST('flux_logistiques', 'restricthtml') : $cdc->flux_logistiques), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<tr>';
    print '<td style="text-align: center;">5.3 '.$langs->trans("prod_et_qualite").'</td>';
    print '<td>';
    $doleditor = new DolEditor('prod_et_qualite', (GETPOST('prod_et_qualite', 'restricthtml') ? GETPOST('prod_et_qualite', 'restricthtml') : $cdc->prod_et_qualite), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<tr>';
    print '<td style="text-align: center;">5.4 '.$langs->trans("commandes_et_expeditions").'</td>';
    print '<td>';
    $doleditor = new DolEditor('commandes_et_expeditions', (GETPOST('commandes_et_expeditions', 'restricthtml') ? GETPOST('commandes_et_expeditions', 'restricthtml') : $cdc->commandes_et_expeditions), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<tr>';
    print '<td style="text-align: center;">5.5 '.$langs->trans("creation_recettes").'</td>';
    print '<td>';
    $doleditor = new DolEditor('creation_recettes', (GETPOST('creation_recettes', 'restricthtml') ? GETPOST('creation_recettes', 'restricthtml') : $cdc->creation_recettes), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<tr>';
    print '<td style="text-align: center;">5.6 '.$langs->trans("reporting_analyse").'</td>';
    print '<td>';
    $doleditor = new DolEditor('reporting_analyse', (GETPOST('reporting_analyse', 'restricthtml') ? GETPOST('reporting_analyse', 'restricthtml') : $cdc->reporting_analyse), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    print '<tr>';
    print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>VI. '.$langs->trans("Interface_user_experience").'<br/><br/></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="text-align: center;">6.1 '.$langs->trans("ux_roles").'</td>';
    print '<td>';
    $doleditor = new DolEditor('ux_roles', (GETPOST('ux_roles', 'restricthtml') ? GETPOST('ux_roles', 'restricthtml') : $cdc->ux_roles), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<tr>';
    print '<td style="text-align: center;">6.2 '.$langs->trans("ux_interfaces").'</td>';
    print '<td>';
    $doleditor = new DolEditor('ux_interfaces', (GETPOST('ux_interfaces', 'restricthtml') ? GETPOST('ux_interfaces', 'restricthtml') : $cdc->ux_interfaces), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<tr>';
    print '<td style="text-align: center;">6.3 '.$langs->trans("ux_kpi_client").'</td>';
    print '<td>';
    $doleditor = new DolEditor('ux_kpi_client', (GETPOST('ux_kpi_client', 'restricthtml') ? GETPOST('ux_kpi_client', 'restricthtml') : $cdc->ux_kpi_client), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    print '<tr>';
    print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>VII. '.$langs->trans("Formation_docu").'<br/><br/></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="text-align: center;">7.1 '.$langs->trans("formation_programme").'</td>';
    print '<td>';
    $doleditor = new DolEditor('formation_programme', (GETPOST('formation_programme', 'restricthtml') ? GETPOST('formation_programme', 'restricthtml') : $cdc->formation_programme), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<tr>';
    print '<td style="text-align: center;">7.2 '.$langs->trans("formation_documentation").'</td>';
    print '<td>';
    $doleditor = new DolEditor('formation_documentation', (GETPOST('formation_documentation', 'restricthtml') ? GETPOST('formation_documentation', 'restricthtml') : $cdc->formation_documentation), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    print '<tr>';
    print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>VIII. '.$langs->trans("Maintenace_support").'<br/><br/></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="text-align: center;">8.1 '.$langs->trans("maintenance_support").'</td>';
    print '<td>';
    $doleditor = new DolEditor('maintenance_support', (GETPOST('maintenance_support', 'restricthtml') ? GETPOST('maintenance_support', 'restricthtml') : $cdc->maintenance_support), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<tr>';
    print '<td style="text-align: center;">8.2 '.$langs->trans("maintenance_mises_a_jour").'</td>';
    print '<td>';
    $doleditor = new DolEditor('maintenance_mises_a_jour', (GETPOST('maintenance_mises_a_jour', 'restricthtml') ? GETPOST('maintenance_mises_a_jour', 'restricthtml') : $cdc->maintenance_mises_a_jour), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    print '<tr>';
    print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>IX. '.$langs->trans("Deploiement_planning").'<br/><br/></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="text-align: center;">9.1 '.$langs->trans("deploiement_calendrier").'</td>';
    print '<td>';
    $doleditor = new DolEditor('deploiement_calendrier', (GETPOST('deploiement_calendrier', 'restricthtml') ? GETPOST('deploiement_calendrier', 'restricthtml') : $cdc->deploiement_calendrier), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<tr>';
    print '<td style="text-align: center;">9.2 '.$langs->trans("deploiement_jalons").'</td>';
    print '<td>';
    $doleditor = new DolEditor('deploiement_jalons', (GETPOST('deploiement_jalons', 'restricthtml') ? GETPOST('deploiement_jalons', 'restricthtml') : $cdc->deploiement_jalons), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    print '<tr>';
    print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>X. '.$langs->trans("Critere_reussite").'<br/><br/></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="text-align: center;">10.1 '.$langs->trans("succes_kpi").'</td>';
    print '<td>';
    $doleditor = new DolEditor('succes_kpi', (GETPOST('succes_kpi', 'restricthtml') ? GETPOST('succes_kpi', 'restricthtml') : $cdc->succes_kpi), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<tr>';
    print '<td style="text-align: center;">10.2 '.$langs->trans("succes_suivi").'</td>';
    print '<td>';
    $doleditor = new DolEditor('succes_suivi', (GETPOST('succes_suivi', 'restricthtml') ? GETPOST('succes_suivi', 'restricthtml') : $cdc->succes_suivi), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    print '<tr>';
    print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>XI. '.$langs->trans("budget_previsionnel").'<br/><br/></td>';
    print '</tr>';
    print '<tr>';
    print '<td></td>';
    print '<td>';
    $doleditor = new DolEditor('budget_previsionnel', (GETPOST('budget_previsionnel', 'restricthtml') ? GETPOST('budget_previsionnel', 'restricthtml') : $cdc->budget_previsionnel), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    print '<tr>';
    print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>XII. '.$langs->trans("modifs_recommandations").'<br/><br/></td>';
    print '</tr>';
    print '<tr>';
    print '<td></td>';
    print '<td>';
    $doleditor = new DolEditor('modifs_recommandations', (GETPOST('modifs_recommandations', 'restricthtml') ? GETPOST('modifs_recommandations', 'restricthtml') : $cdc->modifs_recommandations), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    print '<tr>';
    print '<td colspan="2" style="text-align:center; font-weight: bold; color: rgb(10, 20, 100);"><br/><br/>XIII. '.$langs->trans("Mentions_legal_validation").'<br/><br/></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="text-align: center;">13.1 '.$langs->trans("mentions_confidentialite").'</td>';
    print '<td>';
    $doleditor = new DolEditor('mentions_confidentialite', (GETPOST('mentions_confidentialite', 'restricthtml') ? GETPOST('mentions_confidentialite', 'restricthtml') : $cdc->mentions_confidentialite), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<tr>';
    print '<td style="text-align: center;">13.2 '.$langs->trans("mentions_propriete").'</td>';
    print '<td>';
    $doleditor = new DolEditor('mentions_propriete', (GETPOST('mentions_propriete', 'restricthtml') ? GETPOST('mentions_propriete', 'restricthtml') : $cdc->mentions_propriete), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<tr>';
    print '<td style="text-align: center;">13.3 '.$langs->trans("mentions_conditions_modification").'</td>';
    print '<td>';
    $doleditor = new DolEditor('mentions_conditions_modification', (GETPOST('mentions_conditions_modification', 'restricthtml') ? GETPOST('mentions_conditions_modification', 'restricthtml') : $cdc->mentions_conditions_modification), '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';
    print '<tr>';

    print '</table>';

    print dol_get_fiche_end();

    print $form->buttonsSaveCancel("Save");

    print '</form>';


}else{
    /*
     *  View
    */

    $head = cdc_prepare_head($cdc);

    print dol_get_fiche_head($head, 'card', $langs->trans("Cdc"), -1, 'generic');

    print '
    <div class="arearef heightref valignmiddle centpercent">
        <!-- Start banner content -->
        <div style="vertical-align: middle">
            <div class="pagination paginationref">
                <ul class="right">
                    <li class="noborder litext clearbothonsmartphone">
                        <a href="'.DOL_URL_ROOT.'/custom/rendezvousclient/cdc/list.php">Retour liste</a>
                    </li>
                </ul>
            </div>
            <!-- morehtmlleft -->
            <div class="inline-block floatleft">
                <!-- No photo to show -->
                <div class="floatleft inline-block valignmiddle divphotoref">
                    <div class="photoref">
                        <!-- <span class="fas fa-project-diagram  em088 infobox-project" style="" title="No photo"></span> -->
                        '.img_picto('', 'action').'
                    </div>
                </div>
            </div>
            <div class="inline-block floatleft valignmiddle maxwidth750 marginbottomonly refid refidpadding">
                <div class="refidno">
                    ';
                    print $langs->trans('ThirdParty').' : '.$societe->getNomUrl(1, '');

                    print '<br/>'.$langs->trans('Phone').' ';
                    if($action != 'editphone'){
                        print '<a class="editfielda" href="'.$_SERVER['PHP_SELF'].'?id='.$id.'&action=editphone">'.img_edit().'</a> : ';
                        print dol_print_phone($societe->phone, $societe->country_code, $contactid, $societe->id, 'AC_TEL', '&nbsp;', 'phone', $langs->trans("Phone"));
                    }else{
                        print '<form method="post" action="'.$_SERVER['PHP_SELF'].'?id='.$id.'">';
                        print '<input type="hidden" name="action" value="setphone">';
                        print '<input type="hidden" name="token" value="'.newToken().'">';
                        print '<input type="text" name="phone" value="'.$societe->phone.'">';
                        print '<input type="submit" class="button valignmiddle" value="'.$langs->trans("Modify").'">';
                        print '</form>';
                    }

                    print '<br/>'.$langs->trans('Email').' ';
                    if($action != 'editemail'){
                        print '<a class="editfielda" href="'.$_SERVER['PHP_SELF'].'?id='.$id.'&action=editemail">'.img_edit().'</a> : ';
                        print dol_print_email($societe->email, $societe->id, $societe->id, 'AC_EMAIL', 0, 0, 1);
                    }else{
                        print '<form method="post" action="'.$_SERVER['PHP_SELF'].'?id='.$id.'">';
                        print '<input type="hidden" name="action" value="setemail">';
                        print '<input type="hidden" name="token" value="'.newToken().'">';
                        print '<input type="text" name="email" value="'.$societe->email.'">';
                        print '<input type="submit" class="button valignmiddle" value="'.$langs->trans("Modify").'">';
                        print '</form>';
                    }

                    print '<br/>'.$langs->trans('Project').' ';
                    if($action != 'editprojet'){
                        print '<a class="editfielda" href="'.$_SERVER['PHP_SELF'].'?id='.$id.'&action=editprojet">'.img_edit().'</a> : ';
                        print $projectstatic->getNomUrl(1);
                    }else{
                        print '<form method="post" action="'.$_SERVER['PHP_SELF'].'?id='.$id.'">';
                        print '<input type="hidden" name="action" value="setprojet">';
                        print '<input type="hidden" name="token" value="'.newToken().'">';
                        print $form->selectProjects($projectstatic->id, 'fk_projet');
                        print '<input type="submit" class="button valignmiddle" value="'.$langs->trans("Modify").'">';
                        print '</form>';
                    }
                    print '
                </div>
            </div>
        </div>
        <!-- End banner content -->
    </div>';

    print '<div class="fichecenter">';

    // print '<div class="fichehalfleft">';
    print '<div class="underbanner clearboth"></div>';

    print '<table class="border tableforfield centpercent">';

    print '<tr>';
    print '<td style="width: 30%;">'.$langs->trans("source_cdc").'</td>';
    print '<td>';
    if($cdc->fk_source_cdc){
        print $cdc->getNomUrl($cdc->fk_source_cdc);
    }
    print '</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="width: 30%;">'.$langs->trans("version_num").'</td>';
    print '<td>'.$cdc->version.'</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="width: 30%;">'.$langs->trans("DateD").'</td>';
    print '<td>'.date('d/m/Y H:i:s', strtotime($cdc->date_demo)).'</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="width: 30%;">'.$langs->trans("nb_rdv_amont").'</td>';
    print '<td>'.$cdc->nb_rdv_amont.'</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="width: 30%;">'.$langs->trans("nb_rdv_aval").'</td>';
    print '<td>'.$cdc->nb_rdv_aval.'</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="width: 30%;">'.$langs->trans("nb_jours_formations").'</td>';
    print '<td>'.$cdc->nb_jours_formations.'</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="width: 30%;">'.$langs->trans("DATE_LIVRAISON").'</td>';
    print '<td>'.date('d/m/Y H:i:s', strtotime($cdc->date_livraison)).'</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="font-weight: bold; color: rgb(10, 20, 100);">I. '.$langs->trans("Introduction").'</td>';
    print '<td></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">1.1 '.$langs->trans("intro_contexte_projet").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->intro_contexte_projet).'</td>';;
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">1.2 '.$langs->trans("intro_objectifs_globaux").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->intro_objectifs_globaux).'</td>';;
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">1.3 '.$langs->trans("intro_presentation_client").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->intro_presentation_client).'</td>';;
    print '</tr>';

    print '<tr>';
    print '<td style="font-weight: bold; color: rgb(10, 20, 100);">II. '.$langs->trans("Perimetre_projet").'</td>';
    print '<td></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">2.1 '.$langs->trans("perimetre_projet_delimitation").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->perimetre_projet_delimitation).'</td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">2.2 '.$langs->trans("perimetre_projet_def_processus").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->perimetre_projet_def_processus).'</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="font-weight: bold; color: rgb(10, 20, 100);">III. '.$langs->trans("Besoins_fonctionnels").'</td>';
    print '<td></td>';
    print '<tr>';
    print '<td style="width: 30%;">3.1 '.$langs->trans("besoins_processus_par_site").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->besoins_processus_par_site).'</td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">3.2 '.$langs->trans("besoins_fonctionnels_user").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->besoins_fonctionnels_user).'</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="font-weight: bold; color: rgb(10, 20, 100);">IV. '.$langs->trans("Architecture_tech_logicielle").'</td>';
    print '<td></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">4.1 '.$langs->trans("architecture_solutions").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->architecture_solutions).'</td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">4.2 '.$langs->trans("architecture_modules").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->architecture_modules).'</td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">4.3 '.$langs->trans("architecture_infrastructure").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->architecture_infrastructure).'</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="font-weight: bold; color: rgb(10, 20, 100);">V. '.$langs->trans("Gestion_info_process").'</td>';
    print '<td></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">5.1 '.$langs->trans("process_av_vente").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->process_av_vente).'</td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">5.2 '.$langs->trans("flux_logistiques").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->flux_logistiques).'</td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">5.3 '.$langs->trans("prod_et_qualite").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->prod_et_qualite).'</td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">5.4 '.$langs->trans("commandes_et_expeditions").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->commandes_et_expeditions).'</td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">5.5 '.$langs->trans("creation_recettes").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->creation_recettes).'</td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">5.6 '.$langs->trans("reporting_analyse").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->reporting_analyse).'</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="font-weight: bold; color: rgb(10, 20, 100);">VI. '.$langs->trans("Interface_user_experience").'</td>';
    print '<td></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">6.1 '.$langs->trans("ux_roles").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->ux_roles).'</td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">6.2 '.$langs->trans("ux_interfaces").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->ux_interfaces).'</td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">6.3 '.$langs->trans("ux_kpi_client").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->ux_kpi_client).'</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="font-weight: bold; color: rgb(10, 20, 100);">VII. '.$langs->trans("Formation_docu").'</td>';
    print '<td></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">7.1 '.$langs->trans("formation_programme").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->formation_programme).'</td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">7.2 '.$langs->trans("formation_documentation").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->formation_documentation).'</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="font-weight: bold; color: rgb(10, 20, 100);">VIII. '.$langs->trans("Maintenace_support").'</td>';
    print '<td></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">8.1 '.$langs->trans("maintenance_support").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->maintenance_support).'</td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">8.2 '.$langs->trans("maintenance_mises_a_jour").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->maintenance_mises_a_jour).'</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="font-weight: bold; color: rgb(10, 20, 100);">IX. '.$langs->trans("Deploiement_planning").'</td>';
    print '<td></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">9.1 '.$langs->trans("deploiement_calendrier").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->deploiement_calendrier).'</td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">9.2 '.$langs->trans("deploiement_jalons").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->deploiement_jalons).'</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="font-weight: bold; color: rgb(10, 20, 100);">X. '.$langs->trans("Critere_reussite").'</td>';
    print '<td></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">10.1 '.$langs->trans("succes_kpi").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->succes_kpi).'</td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">10.2 '.$langs->trans("succes_suivi").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->succes_suivi).'</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="font-weight: bold; color: rgb(10, 20, 100); width: 30%">XI. '.$langs->trans("budget_previsionnel").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->budget_previsionnel).'</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="font-weight: bold; color: rgb(10, 20, 100); width: 30%">XII. '.$langs->trans("modifs_recommandations").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->modifs_recommandations).'</td>';
    print '</tr>';

    print '<tr>';
    print '<td style="font-weight: bold; color: rgb(10, 20, 100);">XIII. '.$langs->trans("Mentions_legal_validation").'</td>';
    print '<td></td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">13.1 '.$langs->trans("mentions_confidentialite").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->mentions_confidentialite).'</td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">13.2 '.$langs->trans("mentions_propriete").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->mentions_propriete).'</td>';
    print '</tr>';
    print '<tr>';
    print '<td style="width: 30%;">13.3 '.$langs->trans("mentions_conditions_modification").'</td>';
    print '<td>'.dol_htmlentitiesbr($cdc->mentions_conditions_modification).'</td>';
    print '</tr>';
    print '<tr>';

    print '</table>';

    // FIN div fichehalfleft
    // print '</div>';

    // print '<div class="fichehalfright">';
    // print '<div class="underbanner clearboth"></div>';



    // FIN div fichehalfright
    // print '</div>';

    // FIN div fichecenter
    print '</div>';

    print dol_get_fiche_end();


    print '<div class="clearboth"></div><br>';

    print '<div class="tabsAction">'."\n";

    print dolGetButtonAction('', $langs->trans('NewRDVClient'), 'default', $_SERVER["PHP_SELF"].'?id='.$cdc->rowid.'#'.newToken(), '', 1);

    print dolGetButtonAction('', $langs->trans('GenerateCdc'), 'default', $_SERVER["PHP_SELF"].'?id='.$cdc->rowid.'#'.newToken(), '', 1);

    print dolGetButtonAction('', $langs->trans('Clone'), 'default', $_SERVER["PHP_SELF"].'?id='.$cdc->rowid.'#'.newToken(), '', 1);

    print dolGetButtonAction('', $langs->trans('NewPropal'), 'default', $_SERVER["PHP_SELF"].'?id='.$cdc->rowid.'#'.newToken(), '', 1);

    print dolGetButtonAction('', $langs->trans('NewInvoice'), 'default', $_SERVER["PHP_SELF"].'?id='.$cdc->rowid.'#'.newToken(), '', 1);

    print dolGetButtonAction('', $langs->trans('Modify'), 'default', $_SERVER["PHP_SELF"].'?id='.$cdc->rowid.'&action=edit&token='.newToken(), '', 1);

    print dolGetButtonAction('', $langs->trans('Delete'), 'default', $_SERVER["PHP_SELF"].'?id='.$cdc->rowid.'#'.newToken(), '', 1);

    print dolGetButtonAction('', $langs->trans('SendMail'), 'default', $_SERVER["PHP_SELF"].'?id='.$cdc->rowid.'#'.newToken(), '', 1);

    print '</div>';

}


// End of page
llxFooter();
$db->close();
