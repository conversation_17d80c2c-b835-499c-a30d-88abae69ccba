<?php
/**
 *	\file       rendezvousclient/install.php
 *	\ingroup    rendezvousclient
 *	\brief      Script d'installation du module rendezvousclient
 */

require_once '../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/admin.lib.php';

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

$action = GETPOST('action', 'az09');

/*
 * Actions
*/

if($action == 'install'){
    $error = 0;

    // Lire et exécuter le script SQL principal
    $sqlfile = DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/sql/dolibarr_allversions.sql';

    if (file_exists($sqlfile)) {
        $sql_content = file_get_contents($sqlfile);

        // Diviser le contenu en requêtes individuelles
        $sql_queries = explode(';', $sql_content);

        foreach ($sql_queries as $query) {
            $query = trim($query);
            if (!empty($query) && !preg_match('/^--/', $query)) {
                $resql = $db->query($query);
                if (!$resql) {
                    $error++;
                    setEventMessages($langs->trans("ErrorCreatingTable").": ".$db->lasterror(), null, 'errors');
                }
            }
        }
    } else {
        $error++;
        setEventMessages("Fichier SQL non trouvé: ".$sqlfile, null, 'errors');
    }

    if($error == 0){
        setEventMessages($langs->trans("InstallationCompleted"), null, 'mesgs');
    }
}

/*
 * View
*/

llxHeader('', $langs->trans("InstallModule"));

print load_fiche_titre($langs->trans("InstallModule"), '', 'setup');

print '<div class="info">';
print $langs->trans("InstallModuleDescription");
print '</div>';

print '<form method="post" action="'.$_SERVER['PHP_SELF'].'">';
print '<input type="hidden" name="action" value="install">';
print '<input type="hidden" name="token" value="'.newToken().'">';

print '<div class="center">';
print '<input type="submit" class="button" value="'.$langs->trans("Install").'">';
print '</div>';

print '</form>';

// Vérifier l'état des tables
print '<br><h3>'.$langs->trans("TablesStatus").'</h3>';

$tables_to_check = array(
    'rendez_vous',
    'rendez_vous_socpeople',
    'rendez_vous_site',
    'rendez_vous_site_utilisateur',
    'rendez_vous_site_module',
    'rendez_vous_site_module_constante',
    'rendez_vous_site_module_devspe',
    'rendez_vous_site_module_param',
    'rendez_vous_site_module_extrafields',
    'rendez_vous_synthese_cdc',
    'rendez_vous_demo',
    'rendez_vous_cahier_des_charges',
    'avimm_constante_logiciel',
    'avimm_constante_module',
    'avimm_constante',
    'avimm_constante_inmodule'
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>'.$langs->trans("Table").'</th>';
print '<th>'.$langs->trans("Status").'</th>';
print '<th>'.$langs->trans("Description").'</th>';
print '</tr>';

$tables_descriptions = array(
    'rendez_vous' => 'Table principale des rendez-vous',
    'rendez_vous_socpeople' => 'Liaison rendez-vous / contacts',
    'rendez_vous_site' => 'Sites clients',
    'rendez_vous_site_utilisateur' => 'Types d\'utilisateurs par site',
    'rendez_vous_site_module' => 'Modules sélectionnés par site',
    'rendez_vous_site_module_constante' => 'Constantes par module de site',
    'rendez_vous_site_module_devspe' => 'Développements spécifiques',
    'rendez_vous_site_module_param' => 'Paramétrages par module',
    'rendez_vous_site_module_extrafields' => 'Champs supplémentaires',
    'rendez_vous_synthese_cdc' => 'Synthèse du cahier des charges',
    'rendez_vous_demo' => 'Démos créées',
    'rendez_vous_cahier_des_charges' => 'Cahier des charges principal',
    'avimm_constante_logiciel' => 'Logiciels disponibles',
    'avimm_constante_module' => 'Modules disponibles',
    'avimm_constante' => 'Constantes système',
    'avimm_constante_inmodule' => 'Liaison constantes/modules'
);

$tables_missing = 0;
foreach($tables_to_check as $table){
    $sql = "SHOW TABLES LIKE '".MAIN_DB_PREFIX.$table."'";
    $resql = $db->query($sql);

    print '<tr>';
    print '<td>'.MAIN_DB_PREFIX.$table.'</td>';

    if($resql && $db->num_rows($resql) > 0){
        print '<td><span class="badge badge-status4 badge-status">'.$langs->trans("Exists").'</span></td>';
    } else {
        print '<td><span class="badge badge-status8 badge-status">'.$langs->trans("NotExists").'</span></td>';
        $tables_missing++;
    }

    print '<td>'.($tables_descriptions[$table] ?? '').'</td>';
    print '</tr>';
}

print '</table>';

if ($tables_missing > 0) {
    print '<br><div class="warning">';
    print '<strong>Attention :</strong> '.$tables_missing.' table(s) manquante(s). Veuillez lancer l\'installation pour créer les tables nécessaires.';
    print '</div>';
} else {
    print '<br><div class="ok">';
    print '<strong>Parfait :</strong> Toutes les tables nécessaires sont présentes.';
    print '</div>';
}

// End of page
llxFooter();
$db->close();
