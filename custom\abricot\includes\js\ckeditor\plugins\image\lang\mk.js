﻿/*
Copyright (c) 2003-2013, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'mk', {
	alertUrl: 'Please type the image URL', // MISSING
	alt: 'Alternative Text', // MISSING
	border: 'Border', // MISSING
	btnUpload: 'Send it to the Server', // MISSING
	button2Img: 'Do you want to transform the selected image button on a simple image?', // MISSING
	hSpace: 'HSpace', // MISSING
	img2Button: 'Do you want to transform the selected image on a image button?', // MISSING
	infoTab: 'Image Info', // MISSING
	linkTab: 'Link', // MISSING
	lockRatio: 'Lock Ratio', // MISSING
	menu: 'Image Properties', // MISSING
	resetSize: 'Reset Size', // MISSING
	title: 'Image Properties', // MISSING
	titleButton: 'Image Button Properties', // MISSING
	upload: 'Upload', // MISSING
	urlMissing: 'Image source URL is missing.', // MISSING
	vSpace: 'VSpace', // MISSING
	validateBorder: 'Border must be a whole number.', // MISSING
	validateHSpace: 'HSpace must be a whole number.', // MISSING
	validateVSpace: 'VSpace must be a whole number.' // MISSING
});
