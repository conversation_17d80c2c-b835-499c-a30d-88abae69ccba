<?php
/**
 *	\file       rendezvousclient/site/synthesecdc.php
 *	\ingroup    rendezvousclient
 *	\brief      Synthèse CDC des sites
 */
require_once '../../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/societe/class/societe.class.php';
require_once DOL_DOCUMENT_ROOT.'/projet/class/project.class.php';
dol_include_once('/rendezvousclient/lib/rendezvousclient.lib.php');
dol_include_once('/rendezvousclient/site/class/site.class.php');
dol_include_once('/rendezvousclient/site/class/synthesecdc.class.php');
dol_include_once('/rendezvousclient/cdc/class/cdc.class.php');
require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.formother.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/doleditor.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/files.lib.php';

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'users', 'companies'));

$id = GETPOST('id', 'int');
$action = GETPOST('action', 'az09');

$site = new Site($db);
if($id > 0){
    $site->fetch($id);
}

$projectstatic = new Project($db);
if($site->fk_projet > 0){
    $projectstatic->fetch($site->fk_projet);
}

$societe = new Societe($db);
if($projectstatic->socid > 0){
    $societe->fetch($projectstatic->socid);
}

// Récupérer ou créer la synthèse CDC
$syntheseCDC = new SyntheseCDC($db);
$syntheseCDC_id = 0;

// Vérifier si une synthèse CDC existe déjà pour ce projet
$sql = "SELECT rowid FROM ".MAIN_DB_PREFIX."rendez_vous_synthese_cdc WHERE fk_projet = ".$site->fk_projet;
$resql = $db->query($sql);
if($resql && $db->num_rows($resql) > 0){
    $obj = $db->fetch_object($resql);
    $syntheseCDC_id = $obj->rowid;
    $syntheseCDC->fetch($syntheseCDC_id);
} else {
    // Vérifier si un cahier des charges existe pour ce projet
    $sql = "SELECT rowid FROM ".MAIN_DB_PREFIX."rendez_vous_cahier_des_charges WHERE fk_projet = ".$site->fk_projet;
    $resql = $db->query($sql);
    if($resql && $db->num_rows($resql) > 0){
        $obj = $db->fetch_object($resql);
        $cdc_id = $obj->rowid;

        // Créer une nouvelle synthèse CDC basée sur le cahier des charges existant
        $cdc = new CahierDesCharges($db);
        $cdc->fetch($cdc_id);

        $syntheseCDC->fk_projet = $site->fk_projet;
        $syntheseCDC->fk_source_cdc = $cdc_id;
        $syntheseCDC->version = $cdc->version;
        $syntheseCDC->date_creation = dol_now();
        $syntheseCDC->date_demo = $cdc->date_demo;
        $syntheseCDC->date_livraison = $cdc->date_livraison;
        $syntheseCDC->nb_rdv_amont = $cdc->nb_rdv_amont;
        $syntheseCDC->nb_rdv_aval = $cdc->nb_rdv_aval;
        $syntheseCDC->nb_jours_formations = $cdc->nb_jours_formations;
        $syntheseCDC->contexte_projet = $cdc->intro_contexte_projet;
        $syntheseCDC->objectifs_principaux = $cdc->intro_objectifs_globaux;
        $syntheseCDC->perimetre_projet = $cdc->perimetre_projet_delimitation;

        // Récupérer les informations des modules sélectionnés
        $fonctionnalites = "";
        $contraintes = "";

        // Récupérer les informations des modules sélectionnés pour ce site
        $fonctionnalites = $syntheseCDC->getFonctionnalitesFromModules($site->fk_projet);
        $contraintes = $syntheseCDC->getContraintesFromLogiciels($site->fk_projet);

        $syntheseCDC->fonctionnalites_principales = $fonctionnalites;
        $syntheseCDC->contraintes_techniques = $contraintes;

        $syntheseCDC_id = $syntheseCDC->create();
    } else {
        // Créer une nouvelle synthèse CDC vide
        $syntheseCDC->fk_projet = $site->fk_projet;
        $syntheseCDC->version = "V1";
        $syntheseCDC->date_creation = dol_now();
        $syntheseCDC_id = $syntheseCDC->create();
    }
}

/*
 * Actions
*/

if($action == 'update' && GETPOST('save') == $langs->trans('Save')){
    $syntheseCDC->fk_projet = $site->fk_projet;

    // Mise à jour des informations du tiers si modifiées
    $tiers_phone = GETPOST('tiers_phone', 'alphanohtml');
    $tiers_email = GETPOST('tiers_email', 'email');
    if (!empty($tiers_phone) || !empty($tiers_email)) {
        $sql_update_tiers = "UPDATE ".MAIN_DB_PREFIX."societe SET";
        $updates = array();
        if (!empty($tiers_phone)) {
            $updates[] = " phone = '".$db->escape($tiers_phone)."'";
        }
        if (!empty($tiers_email)) {
            $updates[] = " email = '".$db->escape($tiers_email)."'";
        }
        if (!empty($updates)) {
            $sql_update_tiers .= implode(',', $updates);
            $sql_update_tiers .= " WHERE rowid = ".$societe->id;
            $db->query($sql_update_tiers);
        }
    }

    // Dates
    if(GETPOST('date_creation')){
        $syntheseCDC->date_creation = dol_mktime(GETPOST('date_creationhour', 'int'), GETPOST('date_creationmin', 'int'), 0, GETPOST('date_creationmonth', 'int'), GETPOST('date_creationday', 'int'), GETPOST('date_creationyear', 'int'));
    }
    if(GETPOST('date_demo')){
        $syntheseCDC->date_demo = dol_mktime(GETPOST('date_demohour', 'int'), GETPOST('date_demomin', 'int'), 0, GETPOST('date_demomonth', 'int'), GETPOST('date_demoday', 'int'), GETPOST('date_demoyear', 'int'));
    }
    if(GETPOST('date_livraison')){
        $syntheseCDC->date_livraison = dol_mktime(GETPOST('date_livraisonhour', 'int'), GETPOST('date_livraisonmin', 'int'), 0, GETPOST('date_livraisonmonth', 'int'), GETPOST('date_livraisonday', 'int'), GETPOST('date_livraisonyear', 'int'));
    }

    // Organisation des échanges
    $syntheseCDC->nb_rdv_amont = GETPOST('nb_rdv_amont', 'int');
    $syntheseCDC->nb_rdv_aval = GETPOST('nb_rdv_aval', 'int');
    $syntheseCDC->nb_jours_formations = GETPOST('nb_jours_formations', 'int');

    // Présentation et objectifs
    $syntheseCDC->contexte_projet = GETPOST('contexte_projet', 'restricthtml');
    $syntheseCDC->objectifs_principaux = GETPOST('objectifs_principaux', 'restricthtml');
    $syntheseCDC->perimetre_projet = GETPOST('perimetre_projet', 'restricthtml');

    // Description des besoins
    $syntheseCDC->fonctionnalites_principales = GETPOST('fonctionnalites_principales', 'restricthtml');
    $syntheseCDC->contraintes_techniques = GETPOST('contraintes_techniques', 'restricthtml');

    // Ressources et organisation
    $syntheseCDC->ressources_mobilisees = GETPOST('ressources_mobilisees', 'restricthtml');
    $syntheseCDC->roles_responsabilites = GETPOST('roles_responsabilites', 'restricthtml');

    // Délais et jalons
    $syntheseCDC->delais_globaux = GETPOST('delais_globaux', 'restricthtml');
    $syntheseCDC->jalons_importants = GETPOST('jalons_importants', 'restricthtml');

    // Critères de validation
    $syntheseCDC->criteres_acceptation = GETPOST('criteres_acceptation', 'restricthtml');
    $syntheseCDC->procedure_validation = GETPOST('procedure_validation', 'restricthtml');

    // Budget
    $syntheseCDC->budget_estimatif = GETPOST('budget_estimatif', 'restricthtml');
    $syntheseCDC->modalites_paiement = GETPOST('modalites_paiement', 'restricthtml');

    $result = $syntheseCDC->update();
    if($result > 0){
        setEventMessages($langs->trans("SyntheseCDCUpdated"), null, 'mesgs');
    } else {
        setEventMessages($langs->trans("ErrorSyntheseCDCNotUpdated"), null, 'errors');
    }

    header("Location: ".$_SERVER['PHP_SELF']."?id=".$id);
    exit;
}

// Action pour régénérer automatiquement les fonctionnalités et contraintes
if($action == 'regenerate_auto_content' && GETPOST('confirm') == 'yes'){
    if ($syntheseCDC_id > 0) {
        $fonctionnalites = $syntheseCDC->getFonctionnalitesFromModules($site->fk_projet);
        $contraintes = $syntheseCDC->getContraintesFromLogiciels($site->fk_projet);

        $syntheseCDC->fonctionnalites_principales = $fonctionnalites;
        $syntheseCDC->contraintes_techniques = $contraintes;

        $result = $syntheseCDC->update();
        if($result > 0){
            setEventMessages($langs->trans("AutoContentRegenerated"), null, 'mesgs');
        } else {
            setEventMessages($langs->trans("ErrorRegeneratingContent"), null, 'errors');
        }
    }

    header("Location: ".$_SERVER['PHP_SELF']."?id=".$id);
    exit;
}

/*
 * View
*/

$form = new Form($db);
$formother = new FormOther($db);

$title = $langs->trans('Site').' - '.$langs->trans('SyntheseCDC');

llxHeader('', $title, $help_url);

$site->socid = $societe->id;

$head = site_prepare_head($site);

print dol_get_fiche_head($head, 'synthesecdc', $langs->trans("Site"), -1, 'generic');

print '
<div class="arearef heightref valignmiddle centpercent">
    <!-- Start banner content -->
    <div style="vertical-align: middle">
        <div class="pagination paginationref">
            <ul class="right">
                <li class="noborder litext clearbothonsmartphone">
                    <a href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Retour liste</a>
                </li>
            </ul>
        </div>
        <!-- morehtmlleft -->
        <div class="inline-block floatleft">
            <!-- No photo to show -->
            <div class="floatleft inline-block valignmiddle divphotoref">
                <div class="photoref">
                    '.img_picto('', 'generic').'
                </div>
            </div>
        </div>
        <div class="inline-block floatleft valignmiddle maxwidth750 marginbottomonly refid refidpadding">
            <div class="refidno">
                ';
                print $langs->trans('ThirdParty').' : '.$societe->getNomUrl(1, '');

                print '<br/>'.$langs->trans('Phone').' : '.dol_print_phone($societe->phone, $societe->country_code, $contactid, $societe->id, 'AC_TEL', '&nbsp;', 'phone', $langs->trans("Phone"));

                print '<br/>'.$langs->trans('Email').' : '.dol_print_email($societe->email, $societe->id, $societe->id, 'AC_EMAIL', 0, 0, 1);

                print '<br/>'.$langs->trans('Project').' : '.$projectstatic->getNomUrl(1);
                print '
            </div>
        </div>
    </div>
    <!-- End banner content -->
</div>';
print '<div class="underbanner clearboth"></div>';

print '<form method="post" action="'.$_SERVER['PHP_SELF'].'?id='.$id.'">';
print '<input type="hidden" name="action" value="update">';
print '<input type="hidden" name="token" value="'.newToken().'">';

print '<div class="fichecenter">';
print '<div class="fichehalfleft">';

// Informations du tiers (client)
print '<table class="border centpercent">';
print '<tr class="liste_titre"><td colspan="2">'.$langs->trans("ThirdParty").' ('.$langs->trans("Client").')</td></tr>';

print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("ThirdPartyName").'</td>';
print '<td>'.$societe->getNomUrl(1).'</td>';
print '</tr>';

print '<tr>';
print '<td>'.$langs->trans("Phone").'</td>';
print '<td><input type="text" name="tiers_phone" value="'.$societe->phone.'" size="20"></td>';
print '</tr>';

print '<tr>';
print '<td>'.$langs->trans("Email").'</td>';
print '<td><input type="email" name="tiers_email" value="'.$societe->email.'" size="30"></td>';
print '</tr>';

print '<tr>';
print '<td>'.$langs->trans("Project").'</td>';
print '<td>'.$projectstatic->getNomUrl(1).'</td>';
print '</tr>';

print '</table>';

// Informations générales
print '<br>';
print '<table class="border centpercent">';
print '<tr class="liste_titre"><td colspan="2">'.$langs->trans("GeneralInformation").'</td></tr>';

// Date du cahier des charges
print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("DateCDC").'</td>';
$date_creation = ($syntheseCDC->date_creation ? strtotime($syntheseCDC->date_creation) : dol_now());
print '<td>'.$form->selectDate($date_creation, 'date_creation', 1, 1, '', "formcdc", 1, 0, 0, '', '', '', '', 1, '', '', 'auto').'</td>';
print '</tr>';

// Date de démo
print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("DateD").'</td>';
$date_demo = ($syntheseCDC->date_demo ? strtotime($syntheseCDC->date_demo) : dol_now());
print '<td>'.$form->selectDate($date_demo, 'date_demo', 1, 1, '', "formcdc", 1, 0, 0, '', '', '', '', 1, '', '', 'auto').'</td>';
print '</tr>';

// Date de livraison
print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("DATE_LIVRAISON").'</td>';
$date_livraison = ($syntheseCDC->date_livraison ? strtotime($syntheseCDC->date_livraison) : dol_now());
print '<td>'.$form->selectDate($date_livraison, 'date_livraison', 1, 1, '', "formcdc", 1, 0, 0, '', '', '', '', 1, '', '', 'auto').'</td>';
print '</tr>';

print '</table>';

// Organisation des échanges et accompagnement
print '<br>';
print '<table class="border centpercent">';
print '<tr class="liste_titre"><td colspan="2">'.$langs->trans("OrganisationEchanges").'</td></tr>';

// Nombre de rendez-vous en amont
print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("nb_rdv_amont").'</td>';
print '<td><input type="number" name="nb_rdv_amont" value="'.$syntheseCDC->nb_rdv_amont.'" min="0" max="99"></td>';
print '</tr>';

// Nombre de rendez-vous en aval
print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("nb_rdv_aval").'</td>';
print '<td><input type="number" name="nb_rdv_aval" value="'.$syntheseCDC->nb_rdv_aval.'" min="0" max="99"></td>';
print '</tr>';

// Nombre de jours de formation
print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("nb_jours_formations").'</td>';
print '<td><input type="number" name="nb_jours_formations" value="'.$syntheseCDC->nb_jours_formations.'" min="0" max="99"></td>';
print '</tr>';

print '</table>';

print '</div>'; // Fin fichehalfleft

print '<div class="fichehalfright">';

// Présentation et objectifs du projet
print '<table class="border centpercent">';
print '<tr class="liste_titre"><td colspan="2">'.$langs->trans("PresentationObjectifs").'</td></tr>';

// Contexte du projet
print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("ContexteProjet").'</td>';
print '<td>';
$doleditor = new DolEditor('contexte_projet', $syntheseCDC->contexte_projet, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
print $doleditor->Create(1);
print '</td></tr>';

// Objectifs principaux du projet
print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("ObjectifsPrincipaux").'</td>';
print '<td>';
$doleditor = new DolEditor('objectifs_principaux', $syntheseCDC->objectifs_principaux, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
print $doleditor->Create(1);
print '</td></tr>';

// Périmètre du projet
print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("PerimetreProjet").'</td>';
print '<td>';
$doleditor = new DolEditor('perimetre_projet', $syntheseCDC->perimetre_projet, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
print $doleditor->Create(1);
print '</td></tr>';

print '</table>';

print '</div>'; // Fin fichehalfright
print '</div>'; // Fin fichecenter

// Description des besoins
print '<br>';
print '<table class="border centpercent">';
print '<tr class="liste_titre"><td colspan="2">'.$langs->trans("DescriptionBesoins").'</td></tr>';

// Fonctionnalités principales attendues
print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("FonctionnalitesPrincipales").'</td>';
print '<td>';
print '<div style="margin-bottom: 5px;">';
print '<a href="'.$_SERVER['PHP_SELF'].'?id='.$id.'&action=regenerate_auto_content&confirm=yes&token='.newToken().'" ';
print 'onclick="return confirm(\''.$langs->trans("ConfirmRegenerateAutoContent").'\')" ';
print 'class="button buttongen" title="'.$langs->trans("RegenerateFromModules").'">';
print '<span class="fa fa-refresh"></span> '.$langs->trans("RegenerateAuto");
print '</a>';
print '<span style="margin-left: 10px; font-style: italic; color: #666;">'.$langs->trans("AutoGeneratedFromSelectedModules").'</span>';
print '</div>';
$doleditor = new DolEditor('fonctionnalites_principales', $syntheseCDC->fonctionnalites_principales, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_5, '90%');
print $doleditor->Create(1);
print '</td></tr>';

// Contraintes et aspects techniques
print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("ContraintesTechniques").'</td>';
print '<td>';
print '<div style="margin-bottom: 5px;">';
print '<span style="font-style: italic; color: #666;">'.$langs->trans("AutoGeneratedFromSelectedSoftware").'</span>';
print '</div>';
$doleditor = new DolEditor('contraintes_techniques', $syntheseCDC->contraintes_techniques, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_5, '90%');
print $doleditor->Create(1);
print '</td></tr>';

print '</table>';

// Ressources et organisation
print '<br>';
print '<table class="border centpercent">';
print '<tr class="liste_titre"><td colspan="2">'.$langs->trans("RessourcesOrganisation").'</td></tr>';

// Ressources mobilisées
print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("RessourcesMobilisees").'</td>';
print '<td>';
$doleditor = new DolEditor('ressources_mobilisees', $syntheseCDC->ressources_mobilisees, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
print $doleditor->Create(1);
print '</td></tr>';

// Rôles et responsabilités
print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("RolesResponsabilites").'</td>';
print '<td>';
$doleditor = new DolEditor('roles_responsabilites', $syntheseCDC->roles_responsabilites, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
print $doleditor->Create(1);
print '</td></tr>';

print '</table>';

// Délais et jalons clés
print '<br>';
print '<table class="border centpercent">';
print '<tr class="liste_titre"><td colspan="2">'.$langs->trans("DelaisJalons").'</td></tr>';

// Délais globaux
print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("DelaisGlobaux").'</td>';
print '<td>';
$doleditor = new DolEditor('delais_globaux', $syntheseCDC->delais_globaux, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
print $doleditor->Create(1);
print '</td></tr>';

// Jalons importants
print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("JalonsImportants").'</td>';
print '<td>';
$doleditor = new DolEditor('jalons_importants', $syntheseCDC->jalons_importants, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
print $doleditor->Create(1);
print '</td></tr>';

print '</table>';

// Critères de validation et modalités de recette
print '<br>';
print '<table class="border centpercent">';
print '<tr class="liste_titre"><td colspan="2">'.$langs->trans("CriteresValidation").'</td></tr>';

// Critères d'acceptation des livrables
print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("CriteresAcceptation").'</td>';
print '<td>';
$doleditor = new DolEditor('criteres_acceptation', $syntheseCDC->criteres_acceptation, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
print $doleditor->Create(1);
print '</td></tr>';

// Procédure de validation
print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("ProcedureValidation").'</td>';
print '<td>';
$doleditor = new DolEditor('procedure_validation', $syntheseCDC->procedure_validation, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
print $doleditor->Create(1);
print '</td></tr>';

print '</table>';

// Budget et modalités de paiement
print '<br>';
print '<table class="border centpercent">';
print '<tr class="liste_titre"><td colspan="2">'.$langs->trans("BudgetModalites").'</td></tr>';

// Budget estimatif
print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("BudgetEstimatif").'</td>';
print '<td>';
$doleditor = new DolEditor('budget_estimatif', $syntheseCDC->budget_estimatif, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
print $doleditor->Create(1);
print '</td></tr>';

// Modalités de paiement
print '<tr>';
print '<td style="width: 30%;">'.$langs->trans("ModalitesPaiement").'</td>';
print '<td>';
$doleditor = new DolEditor('modalites_paiement', $syntheseCDC->modalites_paiement, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
print $doleditor->Create(1);
print '</td></tr>';

print '</table>';

// Mention légale
print '<br>';
print '<div class="info" style="background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;">';
print '<h4 style="margin-top: 0;">'.$langs->trans("ImportantNotice").'</h4>';
print '<p><strong>'.$langs->trans("PropositionTechniqueCommerciale").'</strong></p>';
print '<p style="font-style: italic;">';
print $langs->trans("MentionLegaleCDC");
print '</p>';
print '</div>';

print dol_get_fiche_end();

print $form->buttonsSaveCancel("Save");

print '</form>';

// Boutons d'action
print '<div class="tabsAction">'."\n";

// Bouton pour générer un devis avec synthèse CDC intégrée
print '<a class="butAction" href="'.DOL_URL_ROOT.'/comm/propal/card.php?action=create&projectid='.$site->fk_projet.'&socid='.$societe->id.'&origin=project&originid='.$site->fk_projet.'&synthese_cdc_id='.$syntheseCDC->rowid.'">';
print $langs->trans('CreateProposalWithCDC');
print '</a>';

// Bouton pour générer un PDF de la synthèse CDC
print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?id='.$id.'&action=builddoc&token='.newToken().'">';
print $langs->trans('GeneratePDF');
print '</a>';

print '</div>';

// End of page
llxFooter();
$db->close();
