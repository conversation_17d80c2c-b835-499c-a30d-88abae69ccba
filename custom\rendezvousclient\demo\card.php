<?php
/**
 *	\file       rendezvousclient/demo/card.php
 *	\ingroup    rendezvousclient
 *	\brief      Gestion des démos
 */

require_once '../../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/societe/class/societe.class.php';
require_once DOL_DOCUMENT_ROOT.'/projet/class/project.class.php';
dol_include_once('/rendezvousclient/site/class/site.class.php');
dol_include_once('/rendezvousclient/site/class/democreator.class.php');
require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'projects'));

$action = GETPOST('action', 'aZ09');
$id = GETPOST('id', 'int');
$projectid = GETPOST('projectid', 'int');

// Sécurité
if (!$user->rights->rendezvousclient->read) {
    accessforbidden();
}

$site = new Site($db);
$project = new Project($db);
$democreator = new DemoCreator($db);

if ($id > 0) {
    $site->fetch($id);
    $project->fetch($site->fk_projet);
}

if ($projectid > 0) {
    $project->fetch($projectid);
}

/*
 * Actions
 */

if ($action == 'create_demo' && $user->rights->rendezvousclient->write) {
    $modules_selectionnes = GETPOST('modules_selectionnes', 'array:int');
    
    if (empty($modules_selectionnes)) {
        setEventMessages($langs->trans("ErrorNoModuleSelected"), null, 'errors');
    } else {
        setEventMessages($langs->trans("DemoCreationStarted"), null, 'mesgs');
        
        $demo_url = $democreator->createDemo($site, $modules_selectionnes);
        
        if ($demo_url) {
            setEventMessages($langs->trans("DemoCreatedSuccessfully"), null, 'mesgs');
            header("Location: ".$_SERVER['PHP_SELF']."?id=".$id."&demo_url=".urlencode($demo_url));
            exit;
        } else {
            setEventMessages($langs->trans("ErrorDemoCreation").": ".$democreator->error, null, 'errors');
        }
    }
}

if ($action == 'delete_demo' && $user->rights->rendezvousclient->write) {
    $demo_id = GETPOST('demo_id', 'int');
    
    if ($demo_id > 0) {
        $sql = "DELETE FROM ".MAIN_DB_PREFIX."rendez_vous_demo WHERE rowid = ".$demo_id." AND fk_projet = ".$site->fk_projet;
        $resql = $db->query($sql);
        
        if ($resql) {
            setEventMessages($langs->trans("DemoDeleted"), null, 'mesgs');
        } else {
            setEventMessages($langs->trans("ErrorDemoNotDeleted"), null, 'errors');
        }
    }
}

/*
 * View
 */

$form = new Form($db);

$title = $langs->trans('CreateDemo');
llxHeader('', $title);

if ($site->rowid > 0) {
    $head = array();
    $head[0][0] = DOL_URL_ROOT.'/custom/rendezvousclient/site/card.php?id='.$site->rowid;
    $head[0][1] = $langs->trans("Site");
    $head[0][2] = 'site';
    
    $head[1][0] = DOL_URL_ROOT.'/custom/rendezvousclient/demo/card.php?id='.$site->rowid;
    $head[1][1] = $langs->trans("Demos");
    $head[1][2] = 'demos';
    
    print dol_get_fiche_head($head, 'demos', $langs->trans("Site").' - '.$site->nom, -1, 'object_rendezvousclient');
    
    print '<div class="fichecenter">';
    
    // Informations du site
    print '<div class="underbanner clearboth"></div>';
    print '<table class="border centpercent tableforfield">';
    
    print '<tr><td class="titlefield">'.$langs->trans("Project").'</td>';
    print '<td>'.$project->getNomUrl(1).'</td></tr>';
    
    print '<tr><td>'.$langs->trans("NomSite").'</td>';
    print '<td>'.$site->nom.'</td></tr>';
    
    print '<tr><td>'.$langs->trans("TypeSite").'</td>';
    print '<td>'.$site->type.'</td></tr>';
    
    print '</table>';
    
    print '</div>';
    
    print dol_get_fiche_end();
    
    // Formulaire de création de démo
    if ($user->rights->rendezvousclient->write) {
        print '<div class="tabsAction">';
        print '<a class="butAction" href="#" onclick="jQuery(\'#create_demo_form\').toggle(); return false;">'.$langs->trans("CreateDemo").'</a>';
        print '</div>';
        
        print '<div id="create_demo_form" style="display:none;">';
        print '<form method="post" action="'.$_SERVER['PHP_SELF'].'">';
        print '<input type="hidden" name="action" value="create_demo">';
        print '<input type="hidden" name="id" value="'.$site->rowid.'">';
        print '<input type="hidden" name="token" value="'.newToken().'">';
        
        print '<table class="border centpercent">';
        print '<tr class="liste_titre">';
        print '<th colspan="2">'.$langs->trans("CreateDemo").'</th>';
        print '</tr>';
        
        // Sélection des modules (exemple simplifié)
        print '<tr><td class="fieldrequired">'.$langs->trans("ModulesSelectionnes").'</td>';
        print '<td>';
        $modules_example = array(
            1 => 'Sociétés/Contacts',
            2 => 'Factures clients',
            3 => 'Commandes clients',
            4 => 'Projets',
            5 => 'Produits/Services',
            6 => 'Stocks'
        );
        foreach ($modules_example as $key => $label) {
            print '<input type="checkbox" name="modules_selectionnes[]" value="'.$key.'" id="module_'.$key.'"> ';
            print '<label for="module_'.$key.'">'.$label.'</label><br>';
        }
        print '</td></tr>';
        
        print '</table>';
        
        print '<div class="center">';
        print '<input type="submit" class="button" value="'.$langs->trans("CreateDemo").'">';
        print ' <input type="button" class="button button-cancel" value="'.$langs->trans("Cancel").'" onclick="jQuery(\'#create_demo_form\').hide();">';
        print '</div>';
        
        print '</form>';
        print '</div>';
    }
    
    // Liste des démos existantes
    print '<br>';
    print load_fiche_titre($langs->trans("DemosList"), '', '');
    
    $sql = "SELECT rowid, demo_name, demo_url, date_creation, date_expiration, statut";
    $sql .= " FROM ".MAIN_DB_PREFIX."rendez_vous_demo";
    $sql .= " WHERE fk_projet = ".$site->fk_projet;
    $sql .= " ORDER BY date_creation DESC";
    
    $resql = $db->query($sql);
    if ($resql) {
        $num = $db->num_rows($resql);
        
        if ($num > 0) {
            print '<table class="noborder centpercent">';
            print '<tr class="liste_titre">';
            print '<th>'.$langs->trans("DemoName").'</th>';
            print '<th>'.$langs->trans("DemoURL").'</th>';
            print '<th>'.$langs->trans("DateCreation").'</th>';
            print '<th>'.$langs->trans("DateExpiration").'</th>';
            print '<th>'.$langs->trans("Status").'</th>';
            print '<th></th>';
            print '</tr>';
            
            $i = 0;
            while ($i < $num) {
                $obj = $db->fetch_object($resql);
                
                print '<tr class="oddeven">';
                print '<td>'.$obj->demo_name.'</td>';
                print '<td>';
                if ($obj->demo_url) {
                    print '<a href="'.$obj->demo_url.'" target="_blank">'.$langs->trans("OpenDemo").'</a>';
                } else {
                    print '-';
                }
                print '</td>';
                print '<td>'.dol_print_date($db->jdate($obj->date_creation), 'dayhour').'</td>';
                print '<td>';
                if ($obj->date_expiration) {
                    print dol_print_date($db->jdate($obj->date_expiration), 'day');
                } else {
                    print '-';
                }
                print '</td>';
                print '<td>';
                if ($obj->statut == 1) {
                    print '<span class="badge badge-status4 badge-status">Actif</span>';
                } else {
                    print '<span class="badge badge-status8 badge-status">Inactif</span>';
                }
                print '</td>';
                print '<td>';
                if ($user->rights->rendezvousclient->write) {
                    print '<a href="'.$_SERVER['PHP_SELF'].'?id='.$site->rowid.'&action=delete_demo&demo_id='.$obj->rowid.'&token='.newToken().'" ';
                    print 'onclick="return confirm(\''.$langs->trans("ConfirmDeleteDemo").'\')">';
                    print img_delete();
                    print '</a>';
                }
                print '</td>';
                print '</tr>';
                
                $i++;
            }
            
            print '</table>';
        } else {
            print '<div class="opacitymedium">'.$langs->trans("NoDemo").'</div>';
        }
    }
    
} else {
    print $langs->trans("ErrorRecordNotFound");
}

// End of page
llxFooter();
$db->close();
