﻿/*
Copyright (c) 2003-2013, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'hu', {
	alertUrl: 'Töltse ki a kép webcímét',
	alt: 'Buborék szöveg',
	border: 'Keret',
	btnUpload: '<PERSON>üld<PERSON> a szerverre',
	button2Img: 'A kiválasztott képgombból sima képet szeretne csinálni?',
	hSpace: 'Vízsz. táv',
	img2Button: 'A kiválasztott képből képgombot szeretne csinálni?',
	infoTab: 'Alaptulajdonságok',
	linkTab: 'Hivatkozás',
	lockRatio: 'Ar<PERSON>y megtartása',
	menu: 'Kép tulajdons<PERSON>gai',
	resetSize: 'Eredeti méret',
	title: '<PERSON><PERSON><PERSON> tula<PERSON>',
	titleButton: '<PERSON>épgomb tulajdon<PERSON>',
	upload: 'Feltölt<PERSON>',
	urlMissing: 'Hiányzik a kép URL-je',
	vSpace: 'Függ. táv',
	validateBorder: 'A keret méretének egész számot kell beírni!',
	validateHSpace: 'Vízszintes távolságnak egész számot kell beírni!',
	validateVSpace: 'Függőleges távolságnak egész számot kell beírni!'
});
