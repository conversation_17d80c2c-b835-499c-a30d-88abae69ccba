
               body {
               color: black;
               /*   background-color: #e5e5e5;*/
               background-color: #ffffff;
               /*background-color: beige;*/
               margin-top: 2em;
               margin-left: 8%;
               margin-right: 8%;
               }

               h1,h2,h3,h4,h5,h6 {
               margin-top: .5em;
               }

               .title {
               font-size: 200%;
               font-weight: normal;
               }

               .partheading {
               font-size: 100%;
               }

               .chapterheading {
               font-size: 100%;
               }

               .beginsection {
               font-size: 110%;
               }

               .tiny {
               font-size: 40%;
               }

               .scriptsize {
               font-size: 60%;
               }

               .footnotesize {
               font-size: 75%;
               }

               .small {
               font-size: 90%;
               }

               .normalsize {
               font-size: 100%;
               }

               .large {
               font-size: 120%;
               }

               .largecap {
               font-size: 150%;
               }

               .largeup {
               font-size: 200%;
               }

               .huge {
               font-size: 300%;
               }

               .hugecap {
               font-size: 350%;
               }

               pre {
               margin-left: 2em;
               }

               blockquote {
               margin-left: 2em;
               }

               ol {
               list-style-type: decimal;
               }

               ol ol {
               list-style-type: lower-alpha;
               }

               ol ol ol {
               list-style-type: lower-roman;
               }

               ol ol ol ol {
               list-style-type: upper-alpha;
               }

               /*
               .verbatim {
               color: #4d0000;
               }
               */

               tt i {
               font-family: serif;
               }

               .verbatim em {
               font-family: serif;
               }

               .scheme em {
               font-family: serif;
               color: black;
               }

               .scheme {
               color: brown;
               }

               .scheme .keyword {
               color: #990000;
               font-weight: bold;
               }

               .scheme .builtin {
               color: #990000;
               }

               .scheme .variable {
               color: navy;
               }

               .scheme .global {
               color: purple;
               }

               .scheme .selfeval {
               color: green;
               }

               .scheme .comment {
               color:  teal;
               }

               .schemeresponse {
               color: green;
               }

               .navigation {
               color: red;
               text-align: right;
               font-size: medium;
               font-style: italic;
               }

               .disable {
               /* color: #e5e5e5; */
               color: gray;
               }

               .smallcaps {
               font-size: 75%;
               }

               .smallprint {
               color: gray;
               font-size: 75%;
               text-align: right;
               }

               /*
               .smallprint hr {
               text-align: left;
               width: 40%;
               }
               */

               .footnoterule {
               text-align: left;
               width: 40%;
               }

               .colophon {
               color: gray;
               font-size: 80%;
               text-align: right;
               }

               .colophon a {
               color: gray;
               }

               