{"name": "timepicker", "version": "1.2.16", "title": "j<PERSON>y-timepicker", "author": {"name": "<PERSON>", "url": "https://github.com/jont<PERSON>ton"}, "licenses": [{"type": "MIT", "url": "MIT-LICENSE.txt"}], "dependencies": {"jquery": ">=1.7"}, "devDependencies": {"grunt": "~0.4.1", "grunt-contrib-uglify": "~0.2.2"}, "description": "A jQuery timepicker plugin inspired by Google Calendar. It supports both mouse and keyboard navigation.", "keywords": ["timepicker", "time", "picker", "ui", "google calendar"], "homepage": "http://jonthornton.github.com/jquery-timepicker/"}