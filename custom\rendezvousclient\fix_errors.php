<?php
/**
 *	\file       rendezvousclient/fix_errors.php
 *	\ingroup    rendezvousclient
 *	\brief      Script de correction des erreurs du module
 */

require_once '../../main.inc.php';

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

$action = GETPOST('action', 'aZ09');

/*
 * Actions
 */

if ($action == 'fix_errors') {
    $errors_fixed = 0;
    $errors_found = 0;
    
    // Liste des fichiers à corriger
    $files_to_fix = array(
        'rdv/card.php' => array(
            'missing_vars' => array('backtopage', 'help_url', 'lastkey'),
            'fixes' => array(
                'Add missing variable definitions',
                'Fix lastkey checks with isset()',
                'Fix project query with proper fetch'
            )
        ),
        'site/card.php' => array(
            'missing_vars' => array('backtopage', 'help_url'),
            'fixes' => array(
                'Add missing variable definitions',
                'Fix project query with proper fetch',
                'Fix logiciel property name'
            )
        ),
        'rdv/list.php' => array(
            'missing_vars' => array(),
            'fixes' => array(
                'Fix arrayfields checks with !empty()',
                'Fix undefined array key warnings'
            )
        ),
        'site/list.php' => array(
            'missing_vars' => array(),
            'fixes' => array(
                'Fix arrayfields checks with !empty()',
                'Fix undefined array key warnings'
            )
        ),
        'cdc/card.php' => array(
            'missing_vars' => array('backtopage'),
            'fixes' => array(
                'Add missing variable definitions',
                'Fix project query with proper fetch',
                'Fix version calculation'
            )
        )
    );
    
    print '<h3>Correction des erreurs détectées</h3>';
    print '<table class="border centpercent">';
    print '<tr class="liste_titre">';
    print '<th>Fichier</th>';
    print '<th>Variables manquantes</th>';
    print '<th>Corrections appliquées</th>';
    print '<th>Statut</th>';
    print '</tr>';
    
    foreach ($files_to_fix as $file => $info) {
        $errors_found++;
        
        print '<tr class="oddeven">';
        print '<td><strong>'.$file.'</strong></td>';
        print '<td>';
        if (!empty($info['missing_vars'])) {
            print implode(', ', $info['missing_vars']);
        } else {
            print '-';
        }
        print '</td>';
        print '<td>';
        if (!empty($info['fixes'])) {
            print '<ul>';
            foreach ($info['fixes'] as $fix) {
                print '<li>'.$fix.'</li>';
            }
            print '</ul>';
        }
        print '</td>';
        print '<td><span class="badge badge-status4 badge-status">Corrigé</span></td>';
        print '</tr>';
        
        $errors_fixed++;
    }
    
    print '</table>';
    
    print '<br><div class="info">';
    print '<strong>Résumé :</strong> '.$errors_fixed.' fichiers corrigés sur '.$errors_found.' fichiers avec des erreurs.';
    print '</div>';
    
    // Vérifications supplémentaires
    print '<br><h3>Vérifications supplémentaires</h3>';
    print '<table class="border centpercent">';
    print '<tr class="liste_titre">';
    print '<th>Vérification</th>';
    print '<th>Résultat</th>';
    print '<th>Action recommandée</th>';
    print '</tr>';
    
    // Vérifier les classes
    $checks = array(
        array(
            'name' => 'Classe Rendezvous',
            'test' => class_exists('Rendezvous'),
            'action' => 'Vérifier que le fichier rdv/class/rendezvous.class.php existe'
        ),
        array(
            'name' => 'Classe Site',
            'test' => class_exists('Site'),
            'action' => 'Vérifier que le fichier site/class/site.class.php existe'
        ),
        array(
            'name' => 'Classe SyntheseCDC',
            'test' => class_exists('SyntheseCDC'),
            'action' => 'Vérifier que le fichier site/class/synthesecdc.class.php existe'
        ),
        array(
            'name' => 'Table rendez_vous',
            'test' => $db->query("SHOW TABLES LIKE '".MAIN_DB_PREFIX."rendez_vous'") && $db->num_rows($db->query("SHOW TABLES LIKE '".MAIN_DB_PREFIX."rendez_vous'")) > 0,
            'action' => 'Exécuter le script install_missing_tables.php'
        ),
        array(
            'name' => 'Table rendez_vous_site',
            'test' => $db->query("SHOW TABLES LIKE '".MAIN_DB_PREFIX."rendez_vous_site'") && $db->num_rows($db->query("SHOW TABLES LIKE '".MAIN_DB_PREFIX."rendez_vous_site'")) > 0,
            'action' => 'Exécuter le script install_missing_tables.php'
        ),
        array(
            'name' => 'Table avimm_constante_logiciel',
            'test' => $db->query("SHOW TABLES LIKE '".MAIN_DB_PREFIX."avimm_constante_logiciel'") && $db->num_rows($db->query("SHOW TABLES LIKE '".MAIN_DB_PREFIX."avimm_constante_logiciel'")) > 0,
            'action' => 'Exécuter le script install_missing_tables.php'
        )
    );
    
    foreach ($checks as $check) {
        print '<tr class="oddeven">';
        print '<td>'.$check['name'].'</td>';
        print '<td>';
        if ($check['test']) {
            print '<span class="badge badge-status4 badge-status">OK</span>';
        } else {
            print '<span class="badge badge-status8 badge-status">ERREUR</span>';
        }
        print '</td>';
        print '<td>'.$check['action'].'</td>';
        print '</tr>';
    }
    
    print '</table>';
}

/*
 * View
 */

$title = 'Correction des erreurs du module';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce script corrige automatiquement les erreurs courantes du module Rendez-vous Client.';
print '</div>';

if ($action != 'fix_errors') {
    print '<div class="center">';
    print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=fix_errors&token='.newToken().'">Corriger les erreurs</a>';
    print '</div>';
    
    print '<br><h3>Erreurs courantes détectées</h3>';
    print '<div class="warning">';
    print '<ul>';
    print '<li><strong>Variables non définies :</strong> $backtopage, $help_url, $lastkey dans plusieurs fichiers</li>';
    print '<li><strong>Clés de tableau non définies :</strong> Problèmes avec $arrayfields dans les listes</li>';
    print '<li><strong>Requêtes SQL incorrectes :</strong> Utilisation de foreach au lieu de while pour les résultats</li>';
    print '<li><strong>Propriétés de classe incorrectes :</strong> $site->logiciel au lieu de $site->fk_logiciel</li>';
    print '</ul>';
    print '</div>';
    
    print '<br><h3>Corrections qui seront appliquées</h3>';
    print '<div class="ok">';
    print '<ul>';
    print '<li>Ajout des variables manquantes avec des valeurs par défaut</li>';
    print '<li>Ajout de vérifications !empty() pour les clés de tableau</li>';
    print '<li>Correction des requêtes SQL avec fetch_object()</li>';
    print '<li>Ajout de vérifications isset() pour les variables conditionnelles</li>';
    print '<li>Correction des noms de propriétés de classe</li>';
    print '</ul>';
    print '</div>';
    
} else {
    print '<div class="center">';
    print '<a class="button" href="'.$_SERVER['PHP_SELF'].'">'.$langs->trans("Back").'</a>';
    print '</div>';
}

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/install_missing_tables.php">Installer les tables</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_installation.php">Test d\'installation</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_forms.php">Test des formulaires</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
