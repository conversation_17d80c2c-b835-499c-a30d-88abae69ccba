<?php
/**
 *	\file       rendezvousclient/test/test_module.php
 *	\ingroup    rendezvousclient
 *	\brief      Tests unitaires pour le module rendez-vous client
 */

require_once '../../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/projet/class/project.class.php';
dol_include_once('/rendezvousclient/rdv/class/rendezvous.class.php');
dol_include_once('/rendezvousclient/site/class/site.class.php');
dol_include_once('/rendezvousclient/site/class/synthesecdc.class.php');
dol_include_once('/rendezvousclient/site/class/democreator.class.php');

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient'));

// Sécurité
if (!$user->rights->rendezvousclient->read) {
    accessforbidden();
}

$action = GETPOST('action', 'aZ09');

/*
 * Actions
 */

if ($action == 'run_tests') {
    $tests_results = array();
    
    // Test 1: Création d'un projet de test
    $project = new Project($db);
    $project->title = "Projet Test Module RDV Client";
    $project->ref = "TEST_RDV_".date('YmdHis');
    $project->description = "Projet créé pour tester le module rendez-vous client";
    $project->statut = 1;
    $project->public = 0;
    
    $project_id = $project->create($user);
    if ($project_id > 0) {
        $tests_results[] = array('test' => 'Création projet', 'result' => 'OK', 'message' => 'Projet créé avec ID: '.$project_id);
    } else {
        $tests_results[] = array('test' => 'Création projet', 'result' => 'ERREUR', 'message' => $project->error);
    }
    
    if ($project_id > 0) {
        // Test 2: Création d'un rendez-vous
        $rendezvous = new Rendezvous($db);
        $rendezvous->fk_projet = $project_id;
        $rendezvous->objet = "Test RDV - ".date('Y-m-d H:i:s');
        $rendezvous->type = 1;
        $rendezvous->fk_statut = 1;
        $rendezvous->date = dol_now();
        $rendezvous->objectif_client = "Test des fonctionnalités du module";
        $rendezvous->besoin_client = "Validation du bon fonctionnement";
        
        $rdv_id = $rendezvous->create();
        if ($rdv_id > 0) {
            $tests_results[] = array('test' => 'Création rendez-vous', 'result' => 'OK', 'message' => 'RDV créé avec ID: '.$rdv_id);
        } else {
            $tests_results[] = array('test' => 'Création rendez-vous', 'result' => 'ERREUR', 'message' => $rendezvous->error);
        }
        
        // Test 3: Lecture du rendez-vous
        if ($rdv_id > 0) {
            $rendezvous_test = new Rendezvous($db);
            $result = $rendezvous_test->fetch($rdv_id);
            if ($result > 0 && $rendezvous_test->objet == $rendezvous->objet) {
                $tests_results[] = array('test' => 'Lecture rendez-vous', 'result' => 'OK', 'message' => 'RDV lu correctement');
            } else {
                $tests_results[] = array('test' => 'Lecture rendez-vous', 'result' => 'ERREUR', 'message' => 'Erreur lors de la lecture');
            }
            
            // Test 4: Mise à jour du rendez-vous
            $rendezvous_test->compte_rendu = "Compte-rendu de test ajouté";
            $result = $rendezvous_test->update();
            if ($result > 0) {
                $tests_results[] = array('test' => 'Mise à jour rendez-vous', 'result' => 'OK', 'message' => 'RDV mis à jour');
            } else {
                $tests_results[] = array('test' => 'Mise à jour rendez-vous', 'result' => 'ERREUR', 'message' => $rendezvous_test->error);
            }
        }
        
        // Test 5: Création d'un site
        $site = new Site($db);
        $site->fk_projet = $project_id;
        $site->nom = "Site Test - ".date('Y-m-d H:i:s');
        $site->type = "E-commerce";
        $site->description = "Site de test pour le module";
        $site->nombre_utilisateur = 10;
        $site->date = dol_now();
        
        $site_id = $site->create();
        if ($site_id > 0) {
            $tests_results[] = array('test' => 'Création site', 'result' => 'OK', 'message' => 'Site créé avec ID: '.$site_id);
        } else {
            $tests_results[] = array('test' => 'Création site', 'result' => 'ERREUR', 'message' => $site->error);
        }
        
        // Test 6: Création d'une synthèse CDC
        if ($site_id > 0) {
            $synthesecdc = new SyntheseCDC($db);
            $synthesecdc->fk_projet = $project_id;
            $synthesecdc->version = "1.0";
            $synthesecdc->date_creation = dol_now();
            $synthesecdc->contexte_projet = "Contexte de test";
            $synthesecdc->objectifs_principaux = "Objectifs de test";
            
            $cdc_id = $synthesecdc->create();
            if ($cdc_id > 0) {
                $tests_results[] = array('test' => 'Création synthèse CDC', 'result' => 'OK', 'message' => 'CDC créé avec ID: '.$cdc_id);
            } else {
                $tests_results[] = array('test' => 'Création synthèse CDC', 'result' => 'ERREUR', 'message' => $synthesecdc->error);
            }
        }
        
        // Test 7: Test des méthodes utilitaires
        if ($rdv_id > 0) {
            $rendezvous_util = new Rendezvous($db);
            $rendezvous_util->fetch($rdv_id);
            
            $statut_lib = $rendezvous_util->getLibStatut();
            $type_lib = $rendezvous_util->getLibType();
            $nom_url = $rendezvous_util->getNomUrl(1);
            
            if (!empty($statut_lib) && !empty($type_lib) && !empty($nom_url)) {
                $tests_results[] = array('test' => 'Méthodes utilitaires', 'result' => 'OK', 'message' => 'Toutes les méthodes fonctionnent');
            } else {
                $tests_results[] = array('test' => 'Méthodes utilitaires', 'result' => 'ERREUR', 'message' => 'Erreur dans les méthodes utilitaires');
            }
        }
        
        // Test 8: Vérification des tables
        $tables_to_check = array(
            'rendez_vous',
            'rendez_vous_socpeople',
            'rendez_vous_site',
            'rendez_vous_site_utilisateur',
            'rendez_vous_site_module',
            'rendez_vous_synthese_cdc',
            'rendez_vous_demo',
            'avimm_constante_logiciel'
        );
        
        $tables_ok = 0;
        foreach ($tables_to_check as $table) {
            $sql = "SHOW TABLES LIKE '".MAIN_DB_PREFIX.$table."'";
            $resql = $db->query($sql);
            if ($resql && $db->num_rows($resql) > 0) {
                $tables_ok++;
            }
        }
        
        if ($tables_ok == count($tables_to_check)) {
            $tests_results[] = array('test' => 'Vérification tables', 'result' => 'OK', 'message' => 'Toutes les tables existent ('.$tables_ok.'/'.count($tables_to_check).')');
        } else {
            $tests_results[] = array('test' => 'Vérification tables', 'result' => 'ERREUR', 'message' => 'Tables manquantes ('.$tables_ok.'/'.count($tables_to_check).')');
        }
    }
}

/*
 * View
 */

$title = $langs->trans('TestModule');
llxHeader('', $title);

print load_fiche_titre($langs->trans("TestModule"), '', 'object_rendezvousclient');

print '<div class="info">';
print 'Ce script permet de tester les fonctionnalités principales du module Rendez-vous Client.<br>';
print 'Il va créer des données de test et vérifier que toutes les opérations fonctionnent correctement.';
print '</div>';

if ($action != 'run_tests') {
    print '<div class="center">';
    print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=run_tests&token='.newToken().'">'.$langs->trans("RunTests").'</a>';
    print '</div>';
} else {
    print '<h3>Résultats des tests :</h3>';
    
    if (!empty($tests_results)) {
        print '<table class="noborder centpercent">';
        print '<tr class="liste_titre">';
        print '<th>Test</th>';
        print '<th>Résultat</th>';
        print '<th>Message</th>';
        print '</tr>';
        
        foreach ($tests_results as $test) {
            print '<tr class="oddeven">';
            print '<td>'.$test['test'].'</td>';
            print '<td>';
            if ($test['result'] == 'OK') {
                print '<span class="badge badge-status4 badge-status">OK</span>';
            } else {
                print '<span class="badge badge-status8 badge-status">ERREUR</span>';
            }
            print '</td>';
            print '<td>'.$test['message'].'</td>';
            print '</tr>';
        }
        
        print '</table>';
        
        // Résumé
        $nb_ok = 0;
        $nb_error = 0;
        foreach ($tests_results as $test) {
            if ($test['result'] == 'OK') {
                $nb_ok++;
            } else {
                $nb_error++;
            }
        }
        
        print '<br>';
        print '<div class="info">';
        print '<strong>Résumé :</strong> '.$nb_ok.' tests réussis, '.$nb_error.' erreurs sur '.count($tests_results).' tests.';
        print '</div>';
        
        if ($nb_error == 0) {
            print '<div class="ok">';
            print 'Tous les tests sont passés avec succès ! Le module fonctionne correctement.';
            print '</div>';
        } else {
            print '<div class="error">';
            print 'Des erreurs ont été détectées. Veuillez vérifier la configuration du module.';
            print '</div>';
        }
    }
    
    print '<div class="center">';
    print '<a class="button" href="'.$_SERVER['PHP_SELF'].'">'.$langs->trans("RunTestsAgain").'</a>';
    print '</div>';
}

// End of page
llxFooter();
$db->close();
