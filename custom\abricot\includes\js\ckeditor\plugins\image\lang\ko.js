﻿/*
Copyright (c) 2003-2013, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'ko', {
	alertUrl: '이미지 URL을 입력하십시요',
	alt: '이미지 설명',
	border: '테두리',
	btnUpload: '서버로 전송',
	button2Img: 'Do you want to transform the selected image button on a simple image?', // MISSING
	hSpace: '수평여백',
	img2Button: 'Do you want to transform the selected image on a image button?', // MISSING
	infoTab: '이미지 정보',
	linkTab: '링크',
	lockRatio: '비율 유지',
	menu: '이미지 설정',
	resetSize: '원래 크기로',
	title: '이미지 설정',
	titleButton: '이미지버튼 속성',
	upload: '업로드',
	urlMissing: 'Image source URL is missing.', // MISSING
	vSpace: '수직여백',
	validateBorder: 'Border must be a whole number.', // MISSING
	validateHSpace: 'HSpace must be a whole number.', // MISSING
	validateVSpace: 'VSpace must be a whole number.' // MISSING
});
