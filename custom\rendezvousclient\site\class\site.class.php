<?php
/*
 *  \file       rendezvousclient/site/class/site.class.php
 *  \ingroup    rendezvousclient
 *  \brief      Fichier de classe des sites
*/


class Site
{

    /**
	 * @var string ID to identify managed object
	 */
	public $element = 'rendez_vous_site';

	/**
	 * @var string Name of table without prefix where object is stored
	 */
	public $table_element = 'rendez_vous_site';

    public $db;

	public $error;

    public $rowid;

    public $socid;

    public $fk_projet;

    public $date;

    public $nom;

    public $type;

    public $description;

    public $nombre_utilisateur;

    public $fk_logiciel;

    public $autre;

    public $hebergement;

    public $lines_utilisateur = array();

    public $othersite = array();

    /**
	 *	Constructor
	 *
	 *  @param		DoliDB		$db      Database handler
	 */
	public function __construct($db)
	{
		$this->db = $db;
	}

    /**
	 *	recupere les infos d'un site
	 *
	 *	@return 	int			        <0 if KO, >0 if OK
	 */
	public function fetch($id)
	{
        // Check parameters
		if(empty($id)){
			return -1;
		}

        $sql = "SELECT s.rowid, p.fk_soc, s.fk_projet, s.date, s.nom, s.type, s.description, s.nombre_utilisateur, s.fk_logiciel, s.autre, s.hebergement";
        $sql .= " FROM ".MAIN_DB_PREFIX."rendez_vous_site s";
        $sql .= " INNER JOIN ".MAIN_DB_PREFIX."projet p ON p.rowid = s.fk_projet";
        $sql .= " WHERE s.rowid = ".$id;

        $resql = $this->db->query($sql);
        if($resql){
            $obj = $this->db->fetch_object($resql);

            $this->rowid = $obj->rowid;
            $this->socid = $obj->fk_soc;
            $this->fk_projet = $obj->fk_projet;
            $this->date = $obj->date;
            $this->nom = $obj->nom;
            $this->type = $obj->type;
            $this->description = $obj->description;
            $this->nombre_utilisateur = $obj->nombre_utilisateur;
            $this->fk_logiciel = $obj->fk_logiciel;
            $this->autre = $obj->autre;
            $this->hebergement = $obj->hebergement;

            $this->fetch_lines_utilisateur();

            return 1;
		}else{
			return 0;
		}
    }

    public function fetch_lines_utilisateur()
    {
        // Check parameters
		if(empty($this->rowid)){
			return -1;
		}

        $sql = "SELECT rowid, type, descriptif";
        $sql .= " FROM ".MAIN_DB_PREFIX."rendez_vous_site_utilisateur";
        $sql .= " WHERE fk_rendez_vous_site = ".$this->rowid;

        $resql = $this->db->query($sql);
        if($resql){
            $num = $this->db->num_rows($resql);

            $i = 0;
            while($i < $num){
                $obj = $this->db->fetch_object($resql);

                $this->lines_utilisateur[$i]->rowid = $obj->rowid;
                $this->lines_utilisateur[$i]->type = $obj->type;
                $this->lines_utilisateur[$i]->descriptif = $obj->descriptif;

                $i++;
            }

            return 1;
        }
    }

    public function getArrayLogiciel()
    {
        $arrayLogiciel = array();

        $sql = "SELECT rowid, libelle FROM ".MAIN_DB_PREFIX."avimm_constante_logiciel ORDER BY rowid ASC";
        $resql = $this->db->query($sql);

        $num = $this->db->num_rows($resql);

        $c = 0;
        while($c < $num){
            $obj = $this->db->fetch_object($resql);

            $arrayLogiciel[$obj->rowid] = $obj->libelle;

            $c++;
        }

        return $arrayLogiciel;
    }

    public function getLibLogiciel($fk_logiciel)
    {
        global $langs;

        // Check parameters
		if(empty($fk_logiciel)){
			return -1;
		}

        $sql = "SELECT libelle FROM ".MAIN_DB_PREFIX."avimm_constante_logiciel WHERE rowid = ".$fk_logiciel;
        $resql = $this->db->query($sql);

        $obj = $this->db->fetch_object($resql);

        return $obj->libelle;
    }

    public function getNbSite()
    {
        // Check parameters
		if(empty($this->fk_projet)){
			return -1;
		}

        $sql = "SELECT count(rowid) as nb_site FROM ".MAIN_DB_PREFIX."rendez_vous_site WHERE fk_projet = ".$this->fk_projet;

        $resql = $this->db->query($sql);
        if($resql){
            $obj = $this->db->fetch_object($resql);

            return $obj->nb_site;
        }

    }

    public function getOtherSite()
    {
        // Check parameters
        if(empty($this->fk_projet) || empty($this->rowid)){
            return -1;
        }

        $sql = "SELECT s.rowid, p.fk_soc, s.fk_projet, s.date, s.nom, s.type, s.description, s.nombre_utilisateur, s.fk_logiciel, s.autre, s.hebergement";
        $sql .= " FROM ".MAIN_DB_PREFIX."rendez_vous_site s";
        $sql .= " INNER JOIN ".MAIN_DB_PREFIX."projet p ON p.rowid = s.fk_projet";
        $sql .= " WHERE s.fk_projet = ".$this->fk_projet;

        $resql = $this->db->query($sql);
        if($resql){
            $num = $this->db->num_rows($resql);

            $i = 0;

            while($i < $num){
                $obj = $this->db->fetch_object($sql);

                $this->othersite[$i]->rowid = $obj->rowid;
                $this->othersite[$i]->socid = $obj->fk_soc;
                $this->othersite[$i]->fk_projet = $obj->fk_projet;
                $this->othersite[$i]->date = $obj->date;
                $this->othersite[$i]->nom = $obj->nom;
                $this->othersite[$i]->type = $obj->type;
                $this->othersite[$i]->description = $obj->description;
                $this->othersite[$i]->nombre_utilisateur = $obj->nombre_utilisateur;
                $this->othersite[$i]->fk_logiciel = $obj->fk_logiciel;
                $this->othersite[$i]->autre = $obj->autre;
                $this->othersite[$i]->hebergement = $obj->hebergement;

                $i++;
            }

            return 1;
        }
    }

    /**
     * Create a new site
     *
     * @return int ID of created record if OK, -1 if error
     */
    public function create()
    {
        global $user;

        // Check required fields
        if (empty($this->fk_projet) || empty($this->nom)) {
            $this->error = "Missing required fields";
            return -1;
        }

        if (empty($this->date)) {
            $this->date = dol_now();
        }

        $sql = "INSERT INTO ".MAIN_DB_PREFIX."rendez_vous_site (";
        $sql .= "fk_projet, date, nom, type, description, nombre_utilisateur, fk_logiciel, autre, hebergement";
        $sql .= ") VALUES (";
        $sql .= $this->fk_projet.", ";
        $sql .= "'".$this->db->idate($this->date)."', ";
        $sql .= "'".$this->db->escape($this->nom)."', ";
        $sql .= "'".$this->db->escape($this->type)."', ";
        $sql .= "'".$this->db->escape($this->description)."', ";
        $sql .= ($this->nombre_utilisateur ? $this->nombre_utilisateur : "0").", ";
        $sql .= ($this->fk_logiciel ? $this->fk_logiciel : "NULL").", ";
        $sql .= "'".$this->db->escape($this->autre)."', ";
        $sql .= "'".$this->db->escape($this->hebergement)."'";
        $sql .= ")";

        $resql = $this->db->query($sql);
        if($resql){
            $this->rowid = $this->db->last_insert_id(MAIN_DB_PREFIX."rendez_vous_site");
            return $this->rowid;
        } else {
            $this->error = $this->db->lasterror();
            return -1;
        }
    }

    /**
     * Update a site
     *
     * @return int 1 if OK, -1 if error
     */
    public function update()
    {
        // Check required fields
        if (empty($this->rowid) || empty($this->nom)) {
            $this->error = "Missing required fields";
            return -1;
        }

        $sql = "UPDATE ".MAIN_DB_PREFIX."rendez_vous_site SET ";
        $sql .= "date = '".$this->db->idate($this->date)."', ";
        $sql .= "nom = '".$this->db->escape($this->nom)."', ";
        $sql .= "type = '".$this->db->escape($this->type)."', ";
        $sql .= "description = '".$this->db->escape($this->description)."', ";
        $sql .= "nombre_utilisateur = ".($this->nombre_utilisateur ? $this->nombre_utilisateur : "0").", ";
        $sql .= "fk_logiciel = ".($this->fk_logiciel ? $this->fk_logiciel : "NULL").", ";
        $sql .= "autre = '".$this->db->escape($this->autre)."', ";
        $sql .= "hebergement = '".$this->db->escape($this->hebergement)."'";
        $sql .= " WHERE rowid = ".$this->rowid;

        $resql = $this->db->query($sql);
        if($resql){
            return 1;
        } else {
            $this->error = $this->db->lasterror();
            return -1;
        }
    }

    /**
     * Create a user for this site
     *
     * @param string $type Type of user
     * @param string $descriptif Description of user
     * @return int 1 if OK, -1 if error
     */
    public function createUtilisateur($type, $descriptif = '')
    {
        if (empty($this->rowid) || empty($type)) {
            $this->error = "Missing required fields";
            return -1;
        }

        $sql = "INSERT INTO ".MAIN_DB_PREFIX."rendez_vous_site_utilisateur (";
        $sql .= "fk_rendez_vous_site, type, descriptif";
        $sql .= ") VALUES (";
        $sql .= $this->rowid.", ";
        $sql .= "'".$this->db->escape($type)."', ";
        $sql .= "'".$this->db->escape($descriptif)."'";
        $sql .= ")";

        $resql = $this->db->query($sql);
        if($resql){
            return 1;
        } else {
            $this->error = $this->db->lasterror();
            return -1;
        }
    }

    /**
     * Delete all users for this site
     *
     * @return int 1 if OK, -1 if error
     */
    public function deleteAllUtilisateurs()
    {
        if (empty($this->rowid)) {
            $this->error = "Missing site ID";
            return -1;
        }

        $sql = "DELETE FROM ".MAIN_DB_PREFIX."rendez_vous_site_utilisateur WHERE fk_rendez_vous_site = ".$this->rowid;
        $resql = $this->db->query($sql);

        if($resql){
            return 1;
        } else {
            $this->error = $this->db->lasterror();
            return -1;
        }
    }
}

class ParametrageSite
{

    /**
	 * @var string ID to identify managed object
	 */
	public $element = 'rendez_vous_site_module';

	/**
	 * @var string Name of table without prefix where object is stored
	 */
	public $table_element = 'rendez_vous_site_module';

    public $db;

	public $error;

    public $rowid;

    public $modules = array();

    /**
	 *	Constructor
	 *
	 *  @param		DoliDB		$db      Database handler
	 */
	public function __construct($db)
	{
		$this->db = $db;
	}

    public function fetch($fk_site)
    {
        // Check parameters
        if(empty($fk_site)){
            return -1;
        }



        // dans $this->modules il y a un array pour chaque module activé (ou qui ont était activé) du site
        // chaque module a une variable pour savoir si il est activé ou non et un array pour chaque constante activé (ou qui ont était activé) du module
        // chaque module a un array devspe et param (si ils ont au moins 1 ligne dans l'un et l'autre)
        // chaque devspe et param contiendront des arrays, 1 array pour chaque ligne de devspe ou param
        // chaque constante a une variable pour savoir si elle est activée ou non

        // recupere toute les lignes des modules du site
        $sql = "SELECT rowid, fk_module, checked FROM ".MAIN_DB_PREFIX."rendez_vous_site_module WHERE fk_site = ".$fk_site;

        $resql = $this->db->query($sql);
        if($resql){
            $num = $this->db->num_rows($resql);

            $c = 0;
            // lis chaque module
            while($c < $num){
                $obj = $this->db->fetch_object($resql);

                $this->modules[$obj->fk_module]['checked'] = ($obj->checked == 1 ? 1 : 0);

                // recupere tout les dev spe du module
                $sqld = "SELECT rowid, description FROM ".MAIN_DB_PREFIX."rendez_vous_site_module_devspe WHERE fk_site_module = ".$obj->rowid." ORDER BY rowid ASC";

                $resqld = $this->db->query($sqld);
                if($resqld){
                    $numd = $this->db->num_rows($resqld);

                    $cd = 0;
                    // lis chaque dev spe
                    while($cd < $numd){
                        $objd = $this->db->fetch_object($resqld);

                        $this->modules[$obj->fk_module]['devspe'][$cd]->rowid = $objd->rowid;
                        $this->modules[$obj->fk_module]['devspe'][$cd]->description = $objd->description;

                        $cd++;
                    }
                }

                // recupere tout les param du module
                $sqlp = "SELECT rowid, description FROM ".MAIN_DB_PREFIX."rendez_vous_site_module_param WHERE fk_site_module = ".$obj->rowid." ORDER BY rowid ASC";

                $resqlp = $this->db->query($sqlp);
                if($resqlp){
                    $nump = $this->db->num_rows($resqlp);

                    $cp = 0;
                    // lis chaque param
                    while($cp < $nump){
                        $objp = $this->db->fetch_object($resqlp);

                        $this->modules[$obj->fk_module]['param'][$cp]->rowid = $objp->rowid;
                        $this->modules[$obj->fk_module]['param'][$cp]->description = $objp->description;

                        $cp++;
                    }
                }

                // recupere tout les extrafields du module
                $sqle = "SELECT rowid, description FROM ".MAIN_DB_PREFIX."rendez_vous_site_module_extrafields WHERE fk_site_module = ".$obj->rowid." ORDER BY rowid ASC";

                $resqle = $this->db->query($sqle);
                if($resqle){
                    $nume = $this->db->num_rows($resqle);

                    $ce = 0;
                    // lis chaque param
                    while($ce < $nume){
                        $obje = $this->db->fetch_object($resqle);

                        $this->modules[$obj->fk_module]['extrafields'][$ce]->rowid = $obje->rowid;
                        $this->modules[$obj->fk_module]['extrafields'][$ce]->description = $obje->description;

                        $ce++;
                    }
                }

                // recupere les constantes du module
                $sqlc = "SELECT rowid, fk_constante, checked FROM ".MAIN_DB_PREFIX."rendez_vous_site_module_constante WHERE fk_site_module = ".$obj->rowid;

                $resqlc = $this->db->query($sqlc);
                if($resqlc){
                    $numc = $this->db->num_rows($resqlc);

                    $cc = 0;
                    // lis chaque constante
                    while($cc < $numc){
                        $objc = $this->db->fetch_object($resqlc);

                        $this->modules[$obj->fk_module][$objc->fk_constante]['rowid'] = $objc->rowid;
                        $this->modules[$obj->fk_module][$objc->fk_constante]['checked'] = ($objc->checked == 1 ? 1 : 0);

                        $cc++;
                    }
                }
                $c++;
            }
        }

        return 1;
    }
}
