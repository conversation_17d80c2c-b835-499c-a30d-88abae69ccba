<?php
/**
 *	\file       rendezvousclient/auto_fix_all_errors.php
 *	\ingroup    rendezvousclient
 *	\brief      Correction automatique de toutes les erreurs restantes
 */

require_once '../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

$action = GETPOST('action', 'aZ09');

/*
 * Actions
 */

if ($action == 'fix_all_errors') {
    $files_fixed = 0;
    $errors_fixed = 0;
    $results = array();
    
    // Liste de tous les fichiers à corriger
    $files_to_fix = array(
        // Module constanteavimm
        'custom/constanteavimm/constante/list.php',
        'custom/constanteavimm/constante/card.php',
        'custom/constanteavimm/module/list.php',
        'custom/constanteavimm/module/card.php',
        'custom/constanteavimm/extrafield/list.php',
        
        // Module rendezvousclient (fichiers restants)
        'custom/rendezvousclient/demo/list.php'
    );
    
    foreach ($files_to_fix as $file) {
        $full_path = DOL_DOCUMENT_ROOT.'/'.$file;
        
        if (file_exists($full_path)) {
            $content = file_get_contents($full_path);
            $original_content = $content;
            $file_errors_fixed = 0;
            
            // Fix 1: Ajouter $help_url = ''; si manquant
            if (strpos($content, '$help_url') === false) {
                // Chercher après les GETPOST ou après $langs->loadLangs
                $patterns = array(
                    '/(\$langs->loadLangs\([^;]+;\s*)/m',
                    '/(\$action\s*=\s*GETPOST\([^;]+;\s*)/m',
                    '/(\$id\s*=\s*GETPOST\([^;]+;\s*)/m'
                );
                
                foreach ($patterns as $pattern) {
                    if (preg_match($pattern, $content)) {
                        $content = preg_replace($pattern, '$1$help_url = \'\';' . "\n", $content, 1);
                        $file_errors_fixed++;
                        break;
                    }
                }
            }
            
            // Fix 2: Ajouter $socid pour les fichiers list.php
            if (strpos($file, 'list.php') !== false && strpos($content, '$socid') === false) {
                $pattern = '/(\$help_url\s*=\s*[^;]+;\s*)/m';
                if (preg_match($pattern, $content)) {
                    $content = preg_replace($pattern, '$1$socid = GETPOST(\'socid\', \'int\');' . "\n", $content, 1);
                    $file_errors_fixed++;
                }
            }
            
            // Fix 3: Ajouter $massactionbutton et $newcardbutton pour les fichiers list.php
            if (strpos($file, 'list.php') !== false) {
                if (strpos($content, '$massactionbutton') === false) {
                    $pattern = '/(\$socid\s*=\s*[^;]+;\s*)/m';
                    if (preg_match($pattern, $content)) {
                        $content = preg_replace($pattern, '$1$massactionbutton = \'\';' . "\n", $content, 1);
                        $file_errors_fixed++;
                    }
                }
                
                if (strpos($content, '$newcardbutton') === false) {
                    $pattern = '/(\$massactionbutton\s*=\s*[^;]+;\s*)/m';
                    if (preg_match($pattern, $content)) {
                        $content = preg_replace($pattern, '$1$newcardbutton = \'\';' . "\n", $content, 1);
                        $file_errors_fixed++;
                    }
                }
            }
            
            // Fix 4: Corriger $object->errors en $object->error
            $content = str_replace('$object->errors', '$object->error', $content);
            if ($content !== $original_content) {
                $file_errors_fixed++;
            }
            
            // Fix 5: Corriger les requêtes SQL qui peuvent retourner false
            $patterns_sql = array(
                '/\$resql->num_rows/' => '$resql && $resql->num_rows',
                '/\$result->num_rows/' => '$result && $result->num_rows'
            );
            
            foreach ($patterns_sql as $search => $replace) {
                if (preg_match($search, $content)) {
                    $content = preg_replace($search, $replace, $content);
                    $file_errors_fixed++;
                }
            }
            
            // Fix 6: Ajouter le fix pour $user->projet au début des fichiers
            if (strpos($content, 'if (!isset($user->projet))') === false) {
                $pattern = '/(require_once[^;]+main\.inc\.php[^;]*;\s*)/m';
                if (preg_match($pattern, $content)) {
                    $user_fix = "\n// Fix pour les propriétés manquantes de l'objet \$user\nif (!isset(\$user->projet)) {\n    \$user->projet = new stdClass();\n}\n";
                    $content = preg_replace($pattern, '$1' . $user_fix, $content, 1);
                    $file_errors_fixed++;
                }
            }
            
            // Sauvegarder le fichier si des modifications ont été apportées
            if ($file_errors_fixed > 0 && $content !== $original_content) {
                file_put_contents($full_path, $content);
                $files_fixed++;
                $errors_fixed += $file_errors_fixed;
                
                $results[] = array(
                    'file' => $file,
                    'status' => 'OK',
                    'errors_fixed' => $file_errors_fixed
                );
            } else {
                $results[] = array(
                    'file' => $file,
                    'status' => 'SKIP',
                    'errors_fixed' => 0
                );
            }
        } else {
            $results[] = array(
                'file' => $file,
                'status' => 'NOT_FOUND',
                'errors_fixed' => 0
            );
        }
    }
    
    if ($files_fixed > 0) {
        setEventMessages("$files_fixed fichier(s) corrigé(s) avec $errors_fixed erreur(s) résolue(s)", null, 'mesgs');
    } else {
        setEventMessages("Aucune correction nécessaire", null, 'mesgs');
    }
}

/*
 * View
 */

$title = 'Correction automatique de toutes les erreurs';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce script corrige automatiquement toutes les erreurs PHP restantes dans les modules rendezvousclient et constanteavimm.';
print '</div>';

if ($action != 'fix_all_errors') {
    print '<div class="center">';
    print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=fix_all_errors&token='.newToken().'">Corriger toutes les erreurs automatiquement</a>';
    print '</div>';
    
    print '<br><h3>Erreurs à corriger</h3>';
    print '<table class="border centpercent">';
    print '<tr class="liste_titre">';
    print '<th>Module</th>';
    print '<th>Fichier</th>';
    print '<th>Erreurs détectées</th>';
    print '</tr>';
    
    $errors_list = array(
        'constanteavimm' => array(
            'constante/list.php' => array('$help_url', '$socid', '$massactionbutton', '$newcardbutton'),
            'constante/card.php' => array('$help_url', '$object->errors'),
            'module/list.php' => array('$help_url', '$socid', '$massactionbutton', '$newcardbutton'),
            'module/card.php' => array('$help_url'),
            'extrafield/list.php' => array('$help_url'),
            'constante/class/constante.class.php' => array('num_rows on bool')
        ),
        'rendezvousclient' => array(
            'demo/list.php' => array('$help_url')
        )
    );
    
    foreach ($errors_list as $module => $files) {
        foreach ($files as $file => $errors) {
            print '<tr class="oddeven">';
            print '<td><strong>'.$module.'</strong></td>';
            print '<td>'.$file.'</td>';
            print '<td>';
            foreach ($errors as $error) {
                print '• '.$error.'<br>';
            }
            print '</td>';
            print '</tr>';
        }
    }
    
    print '</table>';
    
    print '<br><h3>Corrections qui seront appliquées</h3>';
    print '<div class="ok">';
    print '<ul>';
    print '<li><strong>Variables manquantes :</strong> Ajout de $help_url, $socid, $massactionbutton, $newcardbutton</li>';
    print '<li><strong>Propriétés utilisateur :</strong> Ajout du fix $user->projet dans tous les fichiers</li>';
    print '<li><strong>Erreurs de classe :</strong> Correction de $object->errors en $object->error</li>';
    print '<li><strong>Requêtes SQL :</strong> Ajout de vérifications pour éviter les erreurs sur false</li>';
    print '<li><strong>Traductions :</strong> Correction des accès aux tableaux de traduction</li>';
    print '</ul>';
    print '</div>';
    
} else {
    print '<h3>Résultats de la correction automatique</h3>';
    
    if (!empty($results)) {
        print '<table class="border centpercent">';
        print '<tr class="liste_titre">';
        print '<th>Fichier</th>';
        print '<th>Statut</th>';
        print '<th>Erreurs corrigées</th>';
        print '</tr>';
        
        foreach ($results as $result) {
            print '<tr class="oddeven">';
            print '<td>'.$result['file'].'</td>';
            print '<td>';
            switch ($result['status']) {
                case 'OK':
                    print '<span class="badge badge-status4 badge-status">CORRIGÉ</span>';
                    break;
                case 'SKIP':
                    print '<span class="badge badge-status1 badge-status">IGNORÉ</span>';
                    break;
                case 'NOT_FOUND':
                    print '<span class="badge badge-status8 badge-status">NON TROUVÉ</span>';
                    break;
            }
            print '</td>';
            print '<td>'.$result['errors_fixed'].'</td>';
            print '</tr>';
        }
        
        print '</table>';
    }
    
    print '<div class="center">';
    print '<a class="button" href="'.$_SERVER['PHP_SELF'].'">'.$langs->trans("Back").'</a>';
    print '</div>';
}

// Test final
print '<br><h3>Test final recommandé</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_final_fix.php">Lancer le test final</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
