﻿/*
Copyright (c) 2003-2013, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'zh', {
	alertUrl: '請輸入影像 URL',
	alt: '替代文字',
	border: '邊框',
	btnUpload: '上傳至伺服器',
	button2Img: '要把影像按鈕改成影像嗎？',
	hSpace: '水平距離',
	img2Button: '要把影像改成影像按鈕嗎？',
	infoTab: '影像資訊',
	linkTab: '超連結',
	lockRatio: '等比例',
	menu: '影像屬性',
	resetSize: '重設為原大小',
	title: '影像屬性',
	titleButton: '影像按鈕屬性',
	upload: '上傳',
	urlMissing: 'Image source URL is missing.', // MISSING
	vSpace: '垂直距離',
	validateBorder: 'Border must be a whole number.', // MISSING
	validateHSpace: 'HSpace must be a whole number.', // MISSING
	validateVSpace: 'VSpace must be a whole number.' // MISSING
});
