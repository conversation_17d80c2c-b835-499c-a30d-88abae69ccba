<?php
/**
 *	\file       rendezvousclient/test_forms.php
 *	\ingroup    rendezvousclient
 *	\brief      Test des formulaires du module
 */

require_once '../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/projet/class/project.class.php';
require_once DOL_DOCUMENT_ROOT.'/societe/class/societe.class.php';
dol_include_once('/rendezvousclient/rdv/class/rendezvous.class.php');
dol_include_once('/rendezvousclient/site/class/site.class.php');
dol_include_once('/rendezvousclient/site/class/synthesecdc.class.php');

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient'));

// Sécurité
if (!$user->rights->rendezvousclient->read) {
    accessforbidden();
}

$action = GETPOST('action', 'aZ09');

/*
 * Actions
 */

if ($action == 'test_create_data') {
    $results = array();
    
    // Test 1: Créer une société de test
    $societe = new Societe($db);
    $societe->name = "Société Test Module RDV - ".date('Y-m-d H:i:s');
    $societe->client = 1;
    $societe->email = "<EMAIL>";
    $societe->phone = "0123456789";
    
    $societe_id = $societe->create($user);
    if ($societe_id > 0) {
        $results[] = array('test' => 'Création société', 'result' => 'OK', 'id' => $societe_id);
        
        // Test 2: Créer un projet de test
        $project = new Project($db);
        $project->title = "Projet Test Module RDV - ".date('Y-m-d H:i:s');
        $project->ref = "TEST_RDV_".date('YmdHis');
        $project->description = "Projet créé pour tester le module rendez-vous client";
        $project->statut = 1;
        $project->public = 0;
        $project->fk_soc = $societe_id;
        
        $project_id = $project->create($user);
        if ($project_id > 0) {
            $results[] = array('test' => 'Création projet', 'result' => 'OK', 'id' => $project_id);
            
            // Test 3: Créer un rendez-vous de test
            $rendezvous = new Rendezvous($db);
            $rendezvous->fk_projet = $project_id;
            $rendezvous->objet = "Test RDV - ".date('Y-m-d H:i:s');
            $rendezvous->type = 1;
            $rendezvous->fk_statut = 1;
            $rendezvous->date = dol_now();
            $rendezvous->objectif_client = "Test des fonctionnalités du module";
            $rendezvous->besoin_client = "Validation du bon fonctionnement";
            $rendezvous->numero = "RDV001";
            
            $rdv_id = $rendezvous->create();
            if ($rdv_id > 0) {
                $results[] = array('test' => 'Création rendez-vous', 'result' => 'OK', 'id' => $rdv_id);
            } else {
                $results[] = array('test' => 'Création rendez-vous', 'result' => 'ERREUR', 'error' => $rendezvous->error);
            }
            
            // Test 4: Créer un site de test
            $site = new Site($db);
            $site->fk_projet = $project_id;
            $site->nom = "Site Test - ".date('Y-m-d H:i:s');
            $site->type = "E-commerce";
            $site->description = "Site de test pour le module";
            $site->nombre_utilisateur = 5;
            $site->fk_logiciel = 1; // Dolibarr
            $site->date = dol_now();
            
            $site_id = $site->create();
            if ($site_id > 0) {
                $results[] = array('test' => 'Création site', 'result' => 'OK', 'id' => $site_id);
                
                // Test 5: Ajouter des utilisateurs au site
                $result_user1 = $site->createUtilisateur("Administrateur", "Utilisateur avec tous les droits");
                $result_user2 = $site->createUtilisateur("Commercial", "Utilisateur commercial");
                
                if ($result_user1 > 0 && $result_user2 > 0) {
                    $results[] = array('test' => 'Création utilisateurs site', 'result' => 'OK', 'id' => '2 utilisateurs');
                } else {
                    $results[] = array('test' => 'Création utilisateurs site', 'result' => 'ERREUR', 'error' => $site->error);
                }
                
            } else {
                $results[] = array('test' => 'Création site', 'result' => 'ERREUR', 'error' => $site->error);
            }
            
            // Test 6: Créer une synthèse CDC
            $synthesecdc = new SyntheseCDC($db);
            $synthesecdc->fk_projet = $project_id;
            $synthesecdc->version = "1.0";
            $synthesecdc->date_creation = dol_now();
            $synthesecdc->contexte_projet = "Contexte de test pour le module";
            $synthesecdc->objectifs_principaux = "Objectifs de test";
            
            $cdc_id = $synthesecdc->create();
            if ($cdc_id > 0) {
                $results[] = array('test' => 'Création synthèse CDC', 'result' => 'OK', 'id' => $cdc_id);
            } else {
                $results[] = array('test' => 'Création synthèse CDC', 'result' => 'ERREUR', 'error' => $synthesecdc->error);
            }
            
        } else {
            $results[] = array('test' => 'Création projet', 'result' => 'ERREUR', 'error' => $project->error);
        }
        
    } else {
        $results[] = array('test' => 'Création société', 'result' => 'ERREUR', 'error' => $societe->error);
    }
}

/*
 * View
 */

$title = 'Test des formulaires du module Rendez-vous Client';
llxHeader('', $title);

print load_fiche_titre($title, '', 'object_rendezvousclient');

print '<div class="info">';
print 'Cette page permet de tester les formulaires du module Rendez-vous Client en créant des données de test.';
print '</div>';

if ($action != 'test_create_data') {
    print '<div class="center">';
    print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=test_create_data&token='.newToken().'">Créer des données de test</a>';
    print '</div>';
    
    print '<br>';
    print '<div class="tabsAction">';
    print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rdv/card.php?action=create">Tester formulaire RDV</a>';
    print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/card.php?action=create">Tester formulaire Site</a>';
    print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/synthesecdc.php?action=create_form">Tester formulaire CDC</a>';
    print '</div>';
    
} else {
    print '<h3>Résultats des tests de création :</h3>';
    
    if (!empty($results)) {
        print '<table class="noborder centpercent">';
        print '<tr class="liste_titre">';
        print '<th>Test</th>';
        print '<th>Résultat</th>';
        print '<th>Détails</th>';
        print '</tr>';
        
        foreach ($results as $result) {
            print '<tr class="oddeven">';
            print '<td>'.$result['test'].'</td>';
            print '<td>';
            if ($result['result'] == 'OK') {
                print '<span class="badge badge-status4 badge-status">OK</span>';
            } else {
                print '<span class="badge badge-status8 badge-status">ERREUR</span>';
            }
            print '</td>';
            print '<td>';
            if (isset($result['id'])) {
                print 'ID: '.$result['id'];
            } elseif (isset($result['error'])) {
                print 'Erreur: '.$result['error'];
            }
            print '</td>';
            print '</tr>';
        }
        
        print '</table>';
        
        // Résumé
        $nb_ok = 0;
        $nb_error = 0;
        foreach ($results as $result) {
            if ($result['result'] == 'OK') {
                $nb_ok++;
            } else {
                $nb_error++;
            }
        }
        
        print '<br>';
        print '<div class="info">';
        print '<strong>Résumé :</strong> '.$nb_ok.' tests réussis, '.$nb_error.' erreurs sur '.count($results).' tests.';
        print '</div>';
        
        if ($nb_error == 0) {
            print '<div class="ok">';
            print 'Tous les tests sont passés avec succès ! Les formulaires devraient fonctionner correctement.';
            print '</div>';
            
            print '<br>';
            print '<div class="tabsAction">';
            print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rdv/list.php">Voir les rendez-vous</a>';
            print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Voir les sites</a>';
            print '</div>';
            
        } else {
            print '<div class="error">';
            print 'Des erreurs ont été détectées. Veuillez vérifier la configuration du module et les tables de la base de données.';
            print '</div>';
        }
    }
    
    print '<div class="center">';
    print '<a class="button" href="'.$_SERVER['PHP_SELF'].'">'.$langs->trans("Back").'</a>';
    print '</div>';
}

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_installation.php">Test d\'installation</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
