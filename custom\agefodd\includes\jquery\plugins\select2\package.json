{"name": "select2", "description": "Select2 is a jQuery based replacement for select boxes. It supports searching, remote data sets, and infinite scrolling of results.", "homepage": "https://select2.org", "author": {"name": "<PERSON>", "url": "https://github.com/kevin-brown"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/ivaynberg"}, {"name": "<PERSON>", "url": "https://github.com/alexweissman"}], "repository": {"type": "git", "url": "git://github.com/select2/select2.git"}, "bugs": {"url": "https://github.com/select2/select2/issues"}, "keywords": ["select", "autocomplete", "typeahead", "dropdown", "multiselect", "tag", "tagging"], "license": "MIT", "main": "dist/js/select2.js", "files": ["src", "dist"], "version": "4.0.4", "jspm": {"main": "js/select2", "directories": {"lib": "dist"}}, "devDependencies": {"grunt": "^0.4.5", "grunt-cli": "^0.1.13", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.4.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-nodeunit": "~0.3.3", "grunt-contrib-qunit": "~0.4.0", "grunt-contrib-requirejs": "^0.4.4", "grunt-contrib-symlink": "^0.3.0", "grunt-contrib-uglify": "~0.4.0", "grunt-contrib-watch": "~0.6.0", "grunt-gh-pages": "^0.9.1", "grunt-jekyll": "^0.4.2", "grunt-sass": "^1.0.0", "grunt-saucelabs": "^9.0.0", "node-sass": "^4.5.3", "request": "<=2.81.0", "shrinkwrap": "^0.4.0"}, "dependencies": {"almond": "~0.3.1", "jquery-mousewheel": "~3.1.13"}}