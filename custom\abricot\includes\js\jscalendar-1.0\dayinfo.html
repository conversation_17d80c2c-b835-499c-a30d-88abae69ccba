<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML//EN">
<html> <head>
<title>How to include additional info in day cells</title>
<script type="text/javascript" src="calendar.js"></script>
<script type="text/javascript" src="lang/calendar-en.js"></script>
<script type="text/javascript" src="calendar-setup.js"></script>
<script type="text/javascript">
  // define info for dates in this table:
  var dateInfo = {
    "20050308" : "Mishoo's&nbsp;birthday",
    "20050310" : "foo",
    "20050315" : "bar",
    "20050318" : "25$",
    "20050324" : "60$"
  };
</script>
<style type="text/css">
  @import url(calendar-win2k-1.css);
  .calendar .inf { font-size: 80%; color: #444; }
  .calendar .wn { font-weight: bold; vertical-align: top; }
</style>
</head>

<body>
<h1>How to include additional info in day cells</h1>

<div id="flatcal" style="float: right"></div>

<script type="text/javascript">
  function getDateText(date, d) {
    var inf = dateInfo[date.print("%Y%m%d")];
    if (!inf) {
      return d + "<div class='inf'>&nbsp;</div>";
    } else {
      return d + "<div class='inf'>" + inf + "</div>";
    }
  };
  function flatCallback(cal) {
    if (cal.dateClicked) {
      // do something here
      window.status = "Selected: " + cal.date;
      var inf = dateInfo[cal.date.print("%Y%m%d")];
      if (inf) {
        window.status += ".  Additional info: " + inf;
      }
    }
  };
  Calendar.setup({
     flat: "flatcal",
     dateText: getDateText,
     flatCallback: flatCallback
  });
</script>

<p>The idea is simple:</p>

<ol>
  <li>
    <p>Define a callback that takes two parameters like this:</p>
    <pre>function getDateText(date, d)</pre>
    <p>
      This function will receive the date object as the first
      parameter and the current date number (1..31) as the second (you
      can get it as well by calling date.getDate() but since it's very
      probably useful I thought I'd pass it too so that we can avoid a
      function call).
    </p>
    <p>
      This function <em>must</em> return the text to be inserted in
      the cell of the passed date.  That is, one should at least
      "return d;".
    </p>
  </li>
  <li>
    Pass the above function as the "dateText" parameter to
    Calendar.setup.
  </li>
</ol>

<p>
  The function could simply look like:
</p>

<pre
>  function getDateText(date, d) {
    if (d == 12) {
      return "12th";
    } else if (d == 13) {
      return "bad luck";
    } /* ... etc ... */
  }</pre>

<p>
  but it's easy to imagine that this approach sucks.  For a better
  way, see the source of this page and note the usage of an externally
  defined "dateText" object which maps "date" to "date info", also
  taking into account the year and month.  This object can be easily
  generated from a database, and the getDateText function becomes
  extremely simple (and static).
</p>

<p>
  Cheers!
</p>

<hr />
<address><a href="http://dynarch.com/mishoo/">mishoo</a></address>
<!-- hhmts start --> Last modified: Sat Mar  5 17:18:06 EET 2005 <!-- hhmts end -->
</body> </html>
