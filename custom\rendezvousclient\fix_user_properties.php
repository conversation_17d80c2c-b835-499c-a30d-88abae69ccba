<?php
/**
 *	\file       rendezvousclient/fix_user_properties.php
 *	\ingroup    rendezvousclient
 *	\brief      Correction des propriétés manquantes de l'objet User
 */

require_once '../../main.inc.php';

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

$action = GETPOST('action', 'aZ09');

/*
 * Actions
 */

if ($action == 'fix_user_properties') {
    $fixed = 0;
    $errors = 0;
    
    // Initialiser les propriétés manquantes de l'objet $user
    if (!isset($user->projet)) {
        $user->projet = new stdClass();
        $fixed++;
    }
    
    // Ajouter d'autres propriétés qui pourraient manquer
    $properties_to_check = array(
        'projet' => 'stdClass',
        'rights' => 'stdClass',
        'conf' => 'stdClass'
    );
    
    foreach ($properties_to_check as $property => $type) {
        if (!isset($user->$property)) {
            if ($type == 'stdClass') {
                $user->$property = new stdClass();
            } else {
                $user->$property = null;
            }
            $fixed++;
        }
    }
    
    // Vérifier les droits du module
    if (!isset($user->rights->rendezvousclient)) {
        $user->rights->rendezvousclient = new stdClass();
        $user->rights->rendezvousclient->read = 1;
        $user->rights->rendezvousclient->write = 1;
        $fixed++;
    }
    
    if ($fixed > 0) {
        setEventMessages("$fixed propriété(s) utilisateur corrigée(s)", null, 'mesgs');
    } else {
        setEventMessages("Aucune correction nécessaire", null, 'mesgs');
    }
}

/*
 * View
 */

$title = 'Correction des propriétés utilisateur';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce script corrige les propriétés manquantes de l\'objet $user qui causent des erreurs dans security.lib.php.';
print '</div>';

if ($action != 'fix_user_properties') {
    print '<div class="center">';
    print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=fix_user_properties&token='.newToken().'">Corriger les propriétés utilisateur</a>';
    print '</div>';
    
    print '<br><h3>Erreurs détectées</h3>';
    print '<div class="warning">';
    print '<ul>';
    print '<li><strong>Undefined property: stdClass::$projet</strong> dans core/lib/security.lib.php ligne 352</li>';
    print '<li><strong>Cannot modify header information</strong> causé par les warnings précédents</li>';
    print '</ul>';
    print '</div>';
    
    print '<br><h3>Propriétés utilisateur actuelles</h3>';
    print '<table class="border centpercent">';
    print '<tr class="liste_titre">';
    print '<th>Propriété</th>';
    print '<th>Statut</th>';
    print '<th>Type</th>';
    print '</tr>';
    
    $properties_to_check = array(
        'projet' => 'Propriété projet',
        'rights' => 'Droits utilisateur',
        'conf' => 'Configuration',
        'socid' => 'ID société',
        'admin' => 'Administrateur'
    );
    
    foreach ($properties_to_check as $property => $description) {
        print '<tr class="oddeven">';
        print '<td><strong>'.$description.'</strong></td>';
        print '<td>';
        if (isset($user->$property)) {
            print '<span class="badge badge-status4 badge-status">Définie</span>';
        } else {
            print '<span class="badge badge-status8 badge-status">Manquante</span>';
        }
        print '</td>';
        print '<td>';
        if (isset($user->$property)) {
            print gettype($user->$property);
        } else {
            print '-';
        }
        print '</td>';
        print '</tr>';
    }
    
    print '</table>';
    
} else {
    print '<div class="center">';
    print '<a class="button" href="'.$_SERVER['PHP_SELF'].'">'.$langs->trans("Back").'</a>';
    print '</div>';
}

// Test des propriétés après correction
print '<br><h3>Test des propriétés après correction</h3>';
print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Test</th>';
print '<th>Résultat</th>';
print '<th>Détails</th>';
print '</tr>';

$tests = array(
    array(
        'name' => 'Propriété $user->projet',
        'test' => isset($user->projet),
        'details' => isset($user->projet) ? 'Type: '.gettype($user->projet) : 'Non définie'
    ),
    array(
        'name' => 'Propriété $user->rights',
        'test' => isset($user->rights),
        'details' => isset($user->rights) ? 'Type: '.gettype($user->rights) : 'Non définie'
    ),
    array(
        'name' => 'Droits module rendezvousclient',
        'test' => isset($user->rights->rendezvousclient),
        'details' => isset($user->rights->rendezvousclient) ? 'Définis' : 'Non définis'
    ),
    array(
        'name' => 'Utilisateur admin',
        'test' => $user->admin,
        'details' => $user->admin ? 'Oui' : 'Non'
    ),
    array(
        'name' => 'ID utilisateur',
        'test' => $user->id > 0,
        'details' => 'ID: '.$user->id
    )
);

foreach ($tests as $test) {
    print '<tr class="oddeven">';
    print '<td>'.$test['name'].'</td>';
    print '<td>';
    if ($test['test']) {
        print '<span class="badge badge-status4 badge-status">OK</span>';
    } else {
        print '<span class="badge badge-status8 badge-status">ERREUR</span>';
    }
    print '</td>';
    print '<td>'.$test['details'].'</td>';
    print '</tr>';
}

print '</table>';

// Solution alternative : Hook pour initialiser les propriétés
print '<br><h3>Solution permanente</h3>';
print '<div class="info">';
print 'Pour éviter que ce problème se reproduise, vous pouvez ajouter ce code dans un fichier d\'initialisation :';
print '</div>';

print '<pre style="background: #f5f5f5; padding: 10px; border: 1px solid #ddd;">';
print htmlentities('
// Dans un fichier d\'initialisation ou hook
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

if (!isset($user->rights->rendezvousclient)) {
    $user->rights->rendezvousclient = new stdClass();
    $user->rights->rendezvousclient->read = 1;
    $user->rights->rendezvousclient->write = 1;
}
');
print '</pre>';

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_errors_fixed.php">Test des erreurs</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/install_missing_tables.php">Installer les tables</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_forms.php">Test des formulaires</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
