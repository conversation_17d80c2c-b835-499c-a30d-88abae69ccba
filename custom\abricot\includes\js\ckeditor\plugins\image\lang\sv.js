﻿/*
Copyright (c) 2003-2013, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'sv', {
	alertUrl: 'Var god och ange bildens URL',
	alt: 'Alternativ text',
	border: 'Kant',
	btnUpload: 'Skicka till server',
	button2Img: 'Vill du omvandla den valda bildknappen på en enkel bild?',
	hSpace: 'Horis. marginal',
	img2Button: 'Vill du omvandla den valda bildknappen på en enkel bild?',
	infoTab: 'Bildinformation',
	linkTab: 'Länk',
	lockRatio: 'L<PERSON>s höjd/bredd förhållanden',
	menu: 'Bildegenskaper',
	resetSize: '<PERSON>terställ storlek',
	title: 'Bildegenskaper',
	titleButton: 'Egenskaper för bildknapp',
	upload: 'Ladda upp',
	urlMissing: 'Bildkällans URL saknas.',
	vSpace: 'Vert. marginal',
	validateBorder: 'Kantlinje måste vara ett heltal.',
	validateHSpace: 'HSpace måste vara ett heltal.',
	validateVSpace: 'VSpace måste vara ett heltal.'
});
