-- Script SQL pour créer toutes les tables manquantes du module rendezvousclient
-- À exécuter dans phpMyAdmin ou votre interface de gestion de base de données

-- Table principale des rendez-vous
CREATE TABLE IF NOT EXISTS llx_rendez_vous (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_projet integer NOT NULL,
  fk_statut tinyint DEFAULT 1,
  type tinyint DEFAULT 1,
  numero varchar(50),
  date datetime,
  date_demo datetime,
  date_livraison datetime,
  objet varchar(255),
  objectif_client text,
  besoin_client text,
  reponse_besoin_client text,
  compte_rendu text,
  datec datetime,
  fk_user integer,
  INDEX idx_fk_projet (fk_projet),
  INDEX idx_fk_statut (fk_statut),
  INDEX idx_date (date)
) ENGINE=innodb;

-- Liaison rendez-vous / contacts
CREATE TABLE IF NOT EXISTS llx_rendez_vous_socpeople (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_rendez_vous integer NOT NULL,
  fk_socpeople integer NOT NULL,
  INDEX idx_fk_rendez_vous (fk_rendez_vous),
  INDEX idx_fk_socpeople (fk_socpeople)
) ENGINE=innodb;

-- Sites clients
CREATE TABLE IF NOT EXISTS llx_rendez_vous_site (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_projet integer NOT NULL,
  date datetime,
  nom varchar(255),
  type varchar(100),
  description text,
  nombre_utilisateur integer,
  fk_logiciel integer,
  autre text,
  hebergement text,
  INDEX idx_fk_projet (fk_projet)
) ENGINE=innodb;

-- Types d'utilisateurs par site
CREATE TABLE IF NOT EXISTS llx_rendez_vous_site_utilisateur (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_rendez_vous_site integer NOT NULL,
  type varchar(100),
  descriptif text,
  INDEX idx_fk_site (fk_rendez_vous_site)
) ENGINE=innodb;

-- Modules sélectionnés par site
CREATE TABLE IF NOT EXISTS llx_rendez_vous_site_module (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_site integer NOT NULL,
  fk_module integer NOT NULL,
  checked tinyint DEFAULT 0,
  INDEX idx_fk_site (fk_site),
  INDEX idx_fk_module (fk_module)
) ENGINE=innodb;

-- Constantes par module de site
CREATE TABLE IF NOT EXISTS llx_rendez_vous_site_module_constante (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_site_module integer NOT NULL,
  fk_constante integer NOT NULL,
  checked tinyint DEFAULT 0,
  INDEX idx_fk_site_module (fk_site_module)
) ENGINE=innodb;

-- Développements spécifiques
CREATE TABLE IF NOT EXISTS llx_rendez_vous_site_module_devspe (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_site_module integer NOT NULL,
  description text,
  INDEX idx_fk_site_module (fk_site_module)
) ENGINE=innodb;

-- Paramétrages par module
CREATE TABLE IF NOT EXISTS llx_rendez_vous_site_module_param (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_site_module integer NOT NULL,
  description text,
  INDEX idx_fk_site_module (fk_site_module)
) ENGINE=innodb;

-- Champs supplémentaires
CREATE TABLE IF NOT EXISTS llx_rendez_vous_site_module_extrafields (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_site_module integer NOT NULL,
  description text,
  INDEX idx_fk_site_module (fk_site_module)
) ENGINE=innodb;

-- Cahier des charges principal
CREATE TABLE IF NOT EXISTS llx_rendez_vous_cahier_des_charges (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_projet integer NOT NULL,
  version varchar(10),
  date_creation datetime,
  date_demo datetime,
  date_livraison datetime,
  nb_rdv_amont varchar(100),
  nb_rdv_aval varchar(100),
  nb_jours_formations varchar(100),
  intro_contexte_projet text,
  intro_objectifs_globaux text,
  intro_presentation_client text,
  perimetre_projet_delimitation text,
  perimetre_projet_def_processus text,
  besoins_processus_par_site text,
  besoins_fonctionnels_user text,
  architecture_solutions text,
  architecture_modules text,
  architecture_infrastructure text,
  process_av_vente text,
  flux_logistiques text,
  prod_et_qualite text,
  commandes_et_expeditions text,
  creation_recettes text,
  reporting_analyse text,
  ux_roles text,
  ux_interfaces text,
  ux_kpi_client text,
  formation_programme text,
  formation_documentation text,
  maintenance_support text,
  maintenance_mises_a_jour text,
  deploiement_calendrier text,
  deploiement_jalons text,
  succes_kpi text,
  succes_suivi text,
  budget_previsionnel text,
  modifs_recommandations text,
  mentions_confidentialite text,
  mentions_propriete text,
  mentions_conditions_modification text,
  INDEX idx_fk_projet (fk_projet)
) ENGINE=innodb;

-- Logiciels disponibles
CREATE TABLE IF NOT EXISTS llx_avimm_constante_logiciel (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  libelle varchar(255) NOT NULL,
  description text,
  cahier_des_charges text,
  tarif decimal(24,8) DEFAULT 0,
  active tinyint DEFAULT 1
) ENGINE=innodb;

-- Modules disponibles
CREATE TABLE IF NOT EXISTS llx_avimm_constante_module (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  libelle varchar(255) NOT NULL,
  fk_logiciel integer NOT NULL,
  constante text,
  tarif decimal(24,8) DEFAULT 0,
  cahier_des_charges text,
  synthese_cdc text,
  active tinyint DEFAULT 1,
  INDEX idx_fk_logiciel (fk_logiciel)
) ENGINE=innodb;

-- Constantes système
CREATE TABLE IF NOT EXISTS llx_avimm_constante (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  libelle varchar(255) NOT NULL,
  valeur text,
  dependance_sql text,
  description text,
  debase tinyint DEFAULT 0,
  cahier_des_charges text,
  tarif decimal(24,8) DEFAULT 0,
  active tinyint DEFAULT 1
) ENGINE=innodb;

-- Liaison constantes/modules
CREATE TABLE IF NOT EXISTS llx_avimm_constante_inmodule (
  rowid integer AUTO_INCREMENT PRIMARY KEY,
  fk_constante integer NOT NULL,
  fk_module integer NOT NULL,
  INDEX idx_fk_constante (fk_constante),
  INDEX idx_fk_module (fk_module)
) ENGINE=innodb;

-- Insertion des logiciels par défaut
INSERT IGNORE INTO llx_avimm_constante_logiciel (libelle, description, cahier_des_charges) VALUES
('Dolibarr', 'ERP/CRM Dolibarr', 'Solution ERP/CRM open source complète avec gestion commerciale, comptabilité, stocks, projets, etc.'),
('VTiger', 'CRM VTiger', 'Solution CRM open source avec gestion des contacts, opportunités, campagnes marketing.'),
('WordPress', 'CMS WordPress', 'Système de gestion de contenu web flexible et extensible.'),
('PrestaShop', 'E-commerce PrestaShop', 'Solution e-commerce open source avec gestion catalogue, commandes, paiements.'),
('Magento', 'E-commerce Magento', 'Plateforme e-commerce avancée pour sites marchands complexes.'),
('Autre', 'Autre logiciel', 'Autre solution logicielle selon les besoins spécifiques.');

-- Insertion de quelques modules par défaut pour Dolibarr
INSERT IGNORE INTO llx_avimm_constante_module (libelle, fk_logiciel, synthese_cdc, cahier_des_charges) VALUES
('Sociétés/Contacts', 1, 'Gestion complète des tiers (clients, prospects, fournisseurs) avec fiches détaillées, contacts, historique des interactions.', 'Module de base pour la gestion des relations clients et fournisseurs.'),
('Factures clients', 1, 'Création et gestion des factures clients avec modèles personnalisables, relances automatiques, suivi des paiements.', 'Facturation complète avec gestion des échéances et relances.'),
('Commandes clients', 1, 'Gestion du cycle de vente complet depuis le devis jusqu''à la livraison avec workflow personnalisable.', 'Processus commercial intégré avec validation et suivi.'),
('Projets', 1, 'Gestion de projets avec tâches, temps passé, jalons, budget et facturation au temps passé.', 'Module projet complet avec gestion des ressources.'),
('Produits/Services', 1, 'Catalogue produits avec variantes, prix, stocks, fournisseurs et codes-barres.', 'Gestion complète du référentiel produits.'),
('Stocks', 1, 'Gestion des stocks multi-entrepôts avec mouvements, inventaires et valorisation.', 'Logistique et gestion des stocks avancée.'),
('Comptabilité', 1, 'Module de comptabilité générale avec plan comptable, écritures, balance, grand livre.', 'Comptabilité complète intégrée.'),
('Banques', 1, 'Gestion des comptes bancaires, rapprochements, virements, prélèvements.', 'Gestion bancaire et trésorerie.'),
('Agenda', 1, 'Agenda partagé avec événements, rendez-vous, tâches et rappels.', 'Planification et organisation.'),
('Ressources humaines', 1, 'Gestion des employés, congés, notes de frais, fiches de paie.', 'RH et gestion du personnel.');

-- Insertion de quelques constantes de base
INSERT IGNORE INTO llx_avimm_constante (libelle, valeur, description, debase) VALUES
('MAIN_MODULE_SOCIETE', '1', 'Module Sociétés/Contacts activé', 1),
('MAIN_MODULE_FACTURE', '1', 'Module Factures clients activé', 1),
('MAIN_MODULE_COMMANDE', '1', 'Module Commandes clients activé', 1),
('MAIN_MODULE_PROJET', '1', 'Module Projets activé', 1),
('MAIN_MODULE_PRODUCT', '1', 'Module Produits/Services activé', 1),
('MAIN_MODULE_STOCK', '1', 'Module Stocks activé', 1);

-- Liaison des constantes avec les modules
INSERT IGNORE INTO llx_avimm_constante_inmodule (fk_constante, fk_module) VALUES
(1, 1), -- MAIN_MODULE_SOCIETE avec Sociétés/Contacts
(2, 2), -- MAIN_MODULE_FACTURE avec Factures clients
(3, 3), -- MAIN_MODULE_COMMANDE avec Commandes clients
(4, 4), -- MAIN_MODULE_PROJET avec Projets
(5, 5), -- MAIN_MODULE_PRODUCT avec Produits/Services
(6, 6); -- MAIN_MODULE_STOCK avec Stocks
