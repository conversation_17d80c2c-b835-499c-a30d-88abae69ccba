<?php
/**
 *	\file       rendezvousclient/test_installation.php
 *	\ingroup    rendezvousclient
 *	\brief      Test de l'installation du module
 */

require_once '../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/admin.lib.php';

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

$action = GETPOST('action', 'aZ09');

/*
 * Actions
 */

if ($action == 'install_tables') {
    $error = 0;
    
    // Lire et exécuter le script SQL principal
    $sqlfile = DOL_DOCUMENT_ROOT.'/custom/rendezvousclient/sql/dolibarr_allversions.sql';
    
    if (file_exists($sqlfile)) {
        $sql_content = file_get_contents($sqlfile);
        
        // Diviser le contenu en requêtes individuelles
        $sql_queries = explode(';', $sql_content);
        
        foreach ($sql_queries as $query) {
            $query = trim($query);
            if (!empty($query) && !preg_match('/^--/', $query) && !preg_match('/^\s*$/', $query)) {
                $resql = $db->query($query);
                if (!$resql) {
                    $error++;
                    setEventMessages("Erreur SQL: ".$db->lasterror()." - Requête: ".substr($query, 0, 100)."...", null, 'errors');
                }
            }
        }
    } else {
        $error++;
        setEventMessages("Fichier SQL non trouvé: ".$sqlfile, null, 'errors');
    }
    
    if ($error == 0) {
        setEventMessages("Installation des tables terminée avec succès", null, 'mesgs');
    }
}

/*
 * View
 */

$title = 'Test d\'installation du module Rendez-vous Client';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Cette page permet de tester et installer les tables nécessaires au bon fonctionnement du module Rendez-vous Client.';
print '</div>';

// Bouton d'installation
print '<div class="center" style="margin: 20px 0;">';
print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=install_tables&token='.newToken().'">';
print 'Installer/Mettre à jour les tables';
print '</a>';
print '</div>';

// Vérification des tables
print '<h3>État des tables de la base de données</h3>';

$tables_to_check = array(
    'rendez_vous' => 'Table principale des rendez-vous',
    'rendez_vous_socpeople' => 'Liaison rendez-vous / contacts',
    'rendez_vous_site' => 'Sites clients',
    'rendez_vous_site_utilisateur' => 'Types d\'utilisateurs par site',
    'rendez_vous_site_module' => 'Modules sélectionnés par site',
    'rendez_vous_site_module_constante' => 'Constantes par module de site',
    'rendez_vous_site_module_devspe' => 'Développements spécifiques',
    'rendez_vous_site_module_param' => 'Paramétrages par module',
    'rendez_vous_site_module_extrafields' => 'Champs supplémentaires',
    'rendez_vous_synthese_cdc' => 'Synthèse du cahier des charges',
    'rendez_vous_demo' => 'Démos créées',
    'rendez_vous_cahier_des_charges' => 'Cahier des charges principal',
    'avimm_constante_logiciel' => 'Logiciels disponibles',
    'avimm_constante_module' => 'Modules disponibles',
    'avimm_constante' => 'Constantes système',
    'avimm_constante_inmodule' => 'Liaison constantes/modules'
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Table</th>';
print '<th>Statut</th>';
print '<th>Nombre d\'enregistrements</th>';
print '<th>Description</th>';
print '</tr>';

$tables_ok = 0;
$tables_total = count($tables_to_check);

foreach ($tables_to_check as $table => $description) {
    $sql = "SHOW TABLES LIKE '".MAIN_DB_PREFIX.$table."'";
    $resql = $db->query($sql);
    
    print '<tr class="oddeven">';
    print '<td><strong>'.MAIN_DB_PREFIX.$table.'</strong></td>';
    
    if ($resql && $db->num_rows($resql) > 0) {
        print '<td><span class="badge badge-status4 badge-status">Existe</span></td>';
        
        // Compter les enregistrements
        $sql_count = "SELECT COUNT(*) as nb FROM ".MAIN_DB_PREFIX.$table;
        $resql_count = $db->query($sql_count);
        if ($resql_count) {
            $obj_count = $db->fetch_object($resql_count);
            print '<td>'.$obj_count->nb.' enregistrement(s)</td>';
        } else {
            print '<td>-</td>';
        }
        
        $tables_ok++;
    } else {
        print '<td><span class="badge badge-status8 badge-status">Manquante</span></td>';
        print '<td>-</td>';
    }
    
    print '<td>'.$description.'</td>';
    print '</tr>';
}

print '</table>';

// Résumé
print '<br>';
if ($tables_ok == $tables_total) {
    print '<div class="ok">';
    print '<strong>Parfait !</strong> Toutes les tables sont présentes ('.$tables_ok.'/'.$tables_total.').';
    print '</div>';
} else {
    print '<div class="warning">';
    print '<strong>Attention !</strong> '.$tables_ok.'/'.$tables_total.' tables présentes. '.($tables_total - $tables_ok).' table(s) manquante(s).';
    print '</div>';
}

// Test des données de base
print '<br><h3>Vérification des données de base</h3>';

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Élément</th>';
print '<th>Statut</th>';
print '<th>Détails</th>';
print '</tr>';

// Test logiciels
$sql = "SELECT COUNT(*) as nb FROM ".MAIN_DB_PREFIX."avimm_constante_logiciel WHERE active = 1";
$resql = $db->query($sql);
if ($resql) {
    $obj = $db->fetch_object($resql);
    print '<tr>';
    print '<td>Logiciels disponibles</td>';
    if ($obj->nb > 0) {
        print '<td><span class="badge badge-status4 badge-status">OK</span></td>';
        print '<td>'.$obj->nb.' logiciel(s) configuré(s)</td>';
    } else {
        print '<td><span class="badge badge-status8 badge-status">Vide</span></td>';
        print '<td>Aucun logiciel configuré</td>';
    }
    print '</tr>';
}

// Test modules
$sql = "SELECT COUNT(*) as nb FROM ".MAIN_DB_PREFIX."avimm_constante_module WHERE active = 1";
$resql = $db->query($sql);
if ($resql) {
    $obj = $db->fetch_object($resql);
    print '<tr>';
    print '<td>Modules disponibles</td>';
    if ($obj->nb > 0) {
        print '<td><span class="badge badge-status4 badge-status">OK</span></td>';
        print '<td>'.$obj->nb.' module(s) configuré(s)</td>';
    } else {
        print '<td><span class="badge badge-status8 badge-status">Vide</span></td>';
        print '<td>Aucun module configuré</td>';
    }
    print '</tr>';
}

print '</table>';

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rdv/list.php">Liste des rendez-vous</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Liste des sites</a>';
if (file_exists(DOL_DOCUMENT_ROOT.'/custom/constanteavimm/')) {
    print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/constanteavimm/">Module Constantes AVIMM</a>';
}
print '</div>';

// End of page
llxFooter();
$db->close();
