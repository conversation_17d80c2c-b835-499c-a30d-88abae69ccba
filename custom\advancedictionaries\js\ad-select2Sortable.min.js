/*Select2 Sortable | 1.0.0 | Author: <PERSON> | License : MIT*/
!function(a){a.fn.extend({ADselect2Sortable:function(){var b=Array.prototype.slice.call(arguments,0),c=this.filter("[multiple]");if(0==c.length)this.select2(b[0]);else if(0===b.length||"object"==typeof b[0]){var d={sorter:a=>a.sort(function(c,a){return c.text.localeCompare(a.text)}),createTag:function(){}},e=a.extend([],d,b[0]);"object"!=typeof c.data("select2")&&c.select2(e),c.each(function(){var b=a(this),d=b.siblings(".select2-container").first("ul.select2-selection__rendered");c.select2SetOrderOnInit(b),d.sortable({placeholder:"ui-state-highlight",forcePlaceholderSize:!0,items:"li:not(.select2-search__field)",tolerance:"pointer"}),d.on("sortstop.select2sortable",function(){a(d.find(".select2-selection__choice").get().reverse()).each(function(){var c=a(this).attr("title"),d=b.find("option:contains("+c+")");b.prepend(d)})})})}else if(typeof("string"===b[0])){if(-1==a.inArray(b[0],["destroy"]))throw"Unknown method: "+b[0];"destroy"===b[0]&&c.select2SortableDestroy()}},select2SortableDestroy:function(){var b=this.filter("[multiple]");return b.each(function(){var b=a(this).siblings(".select2-container").first("ul.select2-selection__rendered");b.unbind("sortstop.select2sortable"),b.sortable("destroy")}),b},select2SetOrderOnInit:function(b){var c=b.attr("data-initials"),d=[];if("undefined"!=typeof c){var e=c.split(",");e.length&&(e=e.map(function(a){return a.trim()}),a.each(e,function(a,c){var e=b.find("option[value=\""+c+"\"]");d.push(e),e.remove()}))}d.length&&b.prepend(d)}})}(jQuery);
