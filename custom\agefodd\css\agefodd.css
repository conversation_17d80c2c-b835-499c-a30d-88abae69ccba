/* div#intervenant a span {display: none;}*/
.adminaction a span {
	display: none;
}

/*div#intervenant a:hover span {*/
.adminaction a:hover span {
	display: block;
	position: absolute;
	/* top: 200px; left: 0; */
	width: auto;
	padding: 3px 5px 3px 5px;
	margin: 2px 2px 2px 15px;
	z-index: 100;
	color: #777;
	background: #feffca;
	font: 10px Verdana, sans-serif;
	text-align: center;
	border: 1px solid #b1caec;
}

/* Bordures sur le tableau du formulaire de gestion de la subrogation */
#form_subrogation,#form_subrogation tr,#form_subrogation td {
	border: 0;
}

/* Multi select des destinataires dans les envois de document */
.ui-multiselect {
	width: 60% !important;
	min-width: 550px !important;
}

.ui-multiselect div.selected,.ui-multiselect div.available {
	width: 49.9% !important;
	padding: 0;;
	margin: 0;
}

.agefodd-progress-group{

box-sizing: border-box ;
color: rgb(51, 51, 51) ;
display: inline-block ;
font-family: "Source Sans Pro", "Helvetica Neue", Helvetica, Arial, sans-serif ;
font-size: 14px ;
font-weight: 400 ;
height: 30px ;
width:12em;
line-height: 20px ;
text-size-adjust: 100% ;
-webkit-tap-highlight-color: rgba(0, 0, 0, 0) ;
}

.agefodd-progress-group .agefodd-progress-text {
   font-weight: 600;
}

.agefodd-progress-group .agefodd-progress-number {
   float: right;
}

.agefodd-progress {
   height: 20px;
   overflow: hidden;
   background-color: #d7d7d7;
   border-radius: 4px;
   -webkit-box-shadow: inset 0 1px 2px rgba(0,0,0,.1);
   box-shadow: inset 0 1px 2px rgba(0,0,0,.1);
}

.agefodd-progress.sm, .agefodd-progress-sm, .agefodd-progress.sm .agefodd-progress-bar, .agefodd-progress-sm .agefodd-progress-bar {
   border-radius: 1px;
}
.agefodd-progress.sm, .agefodd-progress-sm {
   height: 10px;
}
.agefodd-progress, .agefodd-progress>.agefodd-progress-bar, .agefodd-progress .agefodd-progress-bar, .agefodd-progress>.agefodd-progress-bar .agefodd-progress-bar {
   border-radius: 1px;
}
.agefodd-progress, .agefodd-progress>.agefodd-progress-bar {
   -webkit-box-shadow: none;
   box-shadow: none;
}

.agefodd-progress, .agefodd-progress>.agefodd-progress-bar, .agefodd-progress .agefodd-progress-bar, .agefodd-progress>.agefodd-progress-bar .agefodd-progress-bar {
   border-radius: 1px;
}
.agefodd-progress, .agefodd-progress>.agefodd-progress-bar {
   -webkit-box-shadow: none;
   box-shadow: none;
}
.agefodd-progress-bar-aqua, .agefodd-progress-bar-info {
   background-color: #00c0ef;
}
.agefodd-progress-bar {
   float: left;
   width: 0;
   height: 100%;
   font-size: 12px;
   line-height: 20px;
   color: #fff;
   text-align: center;
   background-color: #4e9a06;
   -webkit-box-shadow: inset 0 -1px 0 rgba(0,0,0,.15);
   box-shadow: inset 0 -1px 0 rgba(0,0,0,.15);
   -webkit-transition: width .6s ease;
   -o-transition: width .6s ease;
   transition: width .6s ease;
}

.new-trainee-form-container{
	padding: 20px;
	background-color: #f6f6f6;
	border: 1px solid #eaeaea;
}

.new-trainee-form-tile {
	border-bottom: 1px solid #eaeaea;
	padding-bottom: 10px;
}

.new-trainee-form-container tbody tr:last-child td, .new-trainee-form-container tbody tr:last-child th  {
	padding-bottom: 20px;
}

.new-trainee-form-container tfoot tr:first-child td, .new-trainee-form-container tfoot tr:first-child th  {
	padding-top: 20px;
	border-top: 1px solid #eaeaea;
}

/* Css fix for button  class not equiv for <a>  an <button> */
.new-trainee-form-container .button {
	font-size: 1em;
	line-height: 1em;
}

.agf-trainee-table-list .delete-link {
	margin-left: 20px;
}

.agf-trainee-table-list .col-actions{
	white-space: nowrap;
}
.agf-trainee-line.--editmode .col-actions{
	vertical-align: bottom;
}

.agf-trainee-table-list .col-line-number{
	min-width: 20px;
}

.agf-trainee-table-list .col-line-number{
	text-align: center;
}

.agf-trainee-line.--editmode td, .agf-trainee-line.--editmode th{
	background-color: #f6f6f6;
}
