﻿/*
Copyright (c) 2003-2013, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'ro', {
	alertUrl: 'Vă rugăm să scrieţi URL-ul imaginii',
	alt: 'Text alternativ',
	border: 'Margine',
	btnUpload: 'Trimite la server',
	button2Img: 'Do you want to transform the selected image button on a simple image?', // MISSING
	hSpace: 'HSpace',
	img2Button: 'Do you want to transform the selected image on a image button?', // MISSING
	infoTab: 'Informaţii despre imagine',
	linkTab: 'Link (Legătură web)',
	lockRatio: 'Păstrează proporţiile',
	menu: 'Proprietăţile imaginii',
	resetSize: 'Resetează mărimea',
	title: 'Proprietăţile imaginii',
	titleButton: 'Propriet<PERSON><PERSON>i buton imagine (Image Button)',
	upload: 'Încar<PERSON><PERSON>',
	urlMissing: 'Sursa URL a imaginii lipsește.',
	vSpace: 'VSpace',
	validateBorder: 'Bordura trebuie să fie un număr întreg.',
	validateHSpace: 'Hspace trebuie să fie un număr întreg.',
	validateVSpace: 'Vspace trebuie să fie un număr întreg.'
});
