<?php
/**
 *	\file       rendezvousclient/fix_constanteavimm_errors.php
 *	\ingroup    rendezvousclient
 *	\brief      Correction des erreurs dans le module constanteavimm
 */

require_once '../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

$action = GETPOST('action', 'aZ09');

/*
 * Actions
 */

if ($action == 'fix_constanteavimm') {
    $files_to_fix = array();
    $errors_fixed = 0;
    
    // Liste des fichiers à corriger dans constanteavimm
    $constanteavimm_files = array(
        'custom/constanteavimm/logiciel/list.php',
        'custom/constanteavimm/logiciel/card.php',
        'custom/constanteavimm/constante/list.php',
        'custom/constanteavimm/constante/card.php',
        'custom/constanteavimm/module/list.php',
        'custom/constanteavimm/module/card.php',
        'custom/constanteavimm/extrafield/list.php'
    );
    
    foreach ($constanteavimm_files as $file) {
        $full_path = DOL_DOCUMENT_ROOT.'/'.$file;
        
        if (file_exists($full_path)) {
            $content = file_get_contents($full_path);
            $original_content = $content;
            $file_fixed = false;
            
            // Fix 1: Ajouter $help_url = ''; après les autres variables
            if (strpos($content, '$help_url') === false) {
                // Chercher après les GETPOST
                $pattern = '/(\$action\s*=\s*GETPOST\([^;]+;\s*)/';
                if (preg_match($pattern, $content)) {
                    $content = preg_replace($pattern, '$1$help_url = \'\';' . "\n", $content);
                    $file_fixed = true;
                }
            }
            
            // Fix 2: Ajouter $socid = GETPOST('socid', 'int');
            if (strpos($content, '$socid') === false && strpos($file, 'list.php') !== false) {
                $pattern = '/(\$help_url\s*=\s*[^;]+;\s*)/';
                if (preg_match($pattern, $content)) {
                    $content = preg_replace($pattern, '$1$socid = GETPOST(\'socid\', \'int\');' . "\n", $content);
                    $file_fixed = true;
                }
            }
            
            // Fix 3: Ajouter $massactionbutton et $newcardbutton
            if (strpos($content, '$massactionbutton') === false && strpos($file, 'list.php') !== false) {
                $pattern = '/(\$socid\s*=\s*[^;]+;\s*)/';
                if (preg_match($pattern, $content)) {
                    $content = preg_replace($pattern, '$1$massactionbutton = \'\';' . "\n" . '$newcardbutton = \'\';' . "\n", $content);
                    $file_fixed = true;
                }
            }
            
            // Fix 4: Corriger les requêtes SQL qui retournent false
            $content = str_replace('->num_rows', ' && $resql->num_rows', $content);
            if ($content !== $original_content && strpos($content, ' && $resql->num_rows') !== false) {
                $file_fixed = true;
            }
            
            // Sauvegarder le fichier si des modifications ont été apportées
            if ($file_fixed && $content !== $original_content) {
                file_put_contents($full_path, $content);
                $files_to_fix[] = $file;
                $errors_fixed++;
            }
        }
    }
    
    if ($errors_fixed > 0) {
        setEventMessages("$errors_fixed fichier(s) corrigé(s) dans le module constanteavimm", null, 'mesgs');
    } else {
        setEventMessages("Aucune correction nécessaire ou module constanteavimm non trouvé", null, 'mesgs');
    }
}

/*
 * View
 */

$title = 'Correction des erreurs constanteavimm';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce script corrige automatiquement les erreurs dans le module constanteavimm qui est lié au module rendezvousclient.';
print '</div>';

if ($action != 'fix_constanteavimm') {
    print '<div class="center">';
    print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=fix_constanteavimm&token='.newToken().'">Corriger les erreurs constanteavimm</a>';
    print '</div>';
    
    print '<br><h3>Erreurs détectées dans constanteavimm</h3>';
    print '<table class="border centpercent">';
    print '<tr class="liste_titre">';
    print '<th>Fichier</th>';
    print '<th>Erreurs</th>';
    print '<th>Corrections à appliquer</th>';
    print '</tr>';
    
    $errors_detected = array(
        'logiciel/list.php' => array(
            'Undefined variable $help_url',
            'Undefined variable $socid',
            'Undefined variable $massactionbutton',
            'Undefined variable $newcardbutton'
        ),
        'logiciel/card.php' => array(
            'Undefined variable $help_url'
        ),
        'constante/list.php' => array(
            'Undefined variable $help_url',
            'Undefined variable $socid',
            'Undefined variable $massactionbutton',
            'Undefined variable $newcardbutton'
        ),
        'constante/card.php' => array(
            'Undefined variable $help_url'
        ),
        'constante/class/constante.class.php' => array(
            'Attempt to read property "num_rows" on bool'
        ),
        'module/list.php' => array(
            'Undefined variable $help_url',
            'Undefined variable $socid',
            'Undefined variable $massactionbutton',
            'Undefined variable $newcardbutton'
        ),
        'module/card.php' => array(
            'Undefined variable $help_url'
        ),
        'extrafield/list.php' => array(
            'Undefined variable $help_url'
        )
    );
    
    foreach ($errors_detected as $file => $errors) {
        print '<tr class="oddeven">';
        print '<td><strong>'.$file.'</strong></td>';
        print '<td>';
        foreach ($errors as $error) {
            print '• '.$error.'<br>';
        }
        print '</td>';
        print '<td>';
        print '• Ajout des variables manquantes<br>';
        print '• Correction des requêtes SQL<br>';
        print '• Initialisation des propriétés utilisateur';
        print '</td>';
        print '</tr>';
    }
    
    print '</table>';
    
    print '<br><h3>Vérification du module constanteavimm</h3>';
    $constanteavimm_path = DOL_DOCUMENT_ROOT.'/custom/constanteavimm';
    
    if (is_dir($constanteavimm_path)) {
        print '<div class="ok">';
        print 'Module constanteavimm trouvé dans : '.$constanteavimm_path;
        print '</div>';
        
        // Lister les fichiers trouvés
        $files_found = array();
        $files_to_check = array('logiciel', 'constante', 'module', 'extrafield');
        
        foreach ($files_to_check as $subdir) {
            $subdir_path = $constanteavimm_path.'/'.$subdir;
            if (is_dir($subdir_path)) {
                $files = scandir($subdir_path);
                foreach ($files as $file) {
                    if (pathinfo($file, PATHINFO_EXTENSION) == 'php') {
                        $files_found[] = $subdir.'/'.$file;
                    }
                }
            }
        }
        
        if (!empty($files_found)) {
            print '<br><strong>Fichiers PHP trouvés :</strong><br>';
            foreach ($files_found as $file) {
                print '• '.$file.'<br>';
            }
        }
        
    } else {
        print '<div class="warning">';
        print 'Module constanteavimm non trouvé. Chemin attendu : '.$constanteavimm_path;
        print '</div>';
    }
    
} else {
    print '<div class="center">';
    print '<a class="button" href="'.$_SERVER['PHP_SELF'].'">'.$langs->trans("Back").'</a>';
    print '</div>';
}

// Solution manuelle
print '<br><h3>Solution manuelle</h3>';
print '<div class="info">';
print 'Si le script automatique ne fonctionne pas, vous pouvez ajouter manuellement ces lignes au début de chaque fichier PHP du module constanteavimm :';
print '</div>';

print '<pre style="background: #f5f5f5; padding: 10px; border: 1px solid #ddd;">';
print htmlentities('
// Variables manquantes à ajouter après les GETPOST
$help_url = \'\';
$socid = GETPOST(\'socid\', \'int\');
$massactionbutton = \'\';  // Pour les fichiers list.php
$newcardbutton = \'\';     // Pour les fichiers list.php

// Fix pour les propriétés manquantes de l\'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}
');
print '</pre>';

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_final_fix.php">Test final</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
if (is_dir(DOL_DOCUMENT_ROOT.'/custom/constanteavimm')) {
    print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/constanteavimm/">Module constanteavimm</a>';
}
print '</div>';

// End of page
llxFooter();
$db->close();
