<html style="background-color: buttonface; color: buttontext;">

<head>
<meta http-equiv="content-type" content="text/xml; charset=utf-8" />

<title>Simple calendar setup [flat calendar]</title>

  <!-- calendar stylesheet -->
  <link rel="stylesheet" type="text/css" media="all" href="calendar-win2k-cold-1.css" title="win2k-cold-1" />

  <!-- main calendar program -->
  <script type="text/javascript" src="calendar.js"></script>

  <!-- language for the calendar -->
  <script type="text/javascript" src="lang/calendar-en.js"></script>

  <!-- the following script defines the Calendar.setup helper function, which makes
       adding a calendar a matter of 1 or 2 lines of code. -->
  <script type="text/javascript" src="calendar-setup.js"></script>

</head>

<body>

<h2>DHTML Calendar &mdash; for the impatient</h2>

      <blockquote>
        <p>
          This page demonstrates how to setup a flat calendar.  Examples of
          <em>popup</em> calendars are available in <a
          href="simple-1.html">another page</a>.
        </p>
        <p>
          The code in this page uses a helper function defined in
          "calendar-setup.js".  With it you can setup the calendar in
          minutes.  If you're not <em>that</em> impatient, ;-) <a
          href="doc/html/reference.html">complete documenation</a> is
          available.
        </p>
      </blockquote>



<hr />

<div style="float: right; margin-left: 1em; margin-bottom: 1em;"
id="calendar-container"></div>

<script type="text/javascript">
  function dateChanged(calendar) {
    // Beware that this function is called even if the end-user only
    // changed the month/year.  In order to determine if a date was
    // clicked you can use the dateClicked property of the calendar:
    if (calendar.dateClicked) {
      // OK, a date was clicked, redirect to /yyyy/mm/dd/index.php
      var y = calendar.date.getFullYear();
      var m = calendar.date.getMonth();     // integer, 0..11
      var d = calendar.date.getDate();      // integer, 1..31
      // redirect...
      window.location = "/" + y + "/" + m + "/" + d + "/index.php";
    }
  };

  Calendar.setup(
    {
      flat         : "calendar-container", // ID of the parent element
      flatCallback : dateChanged           // our callback function
    }
  );
</script>

<p>The positioning of the DIV that contains the calendar is entirely your
job.  For instance, the "calendar-container" DIV from this page has the
following style: "float: right; margin-left: 1em; margin-bottom: 1em".</p>

<p>Following there is the code that has been used to create this calendar.
You can find the full description of the <tt>Calendar.setup()</tt> function
in the <a href="doc/html/reference.html">calendar documenation</a>.</p>

<pre
>&lt;div style="float: right; margin-left: 1em; margin-bottom: 1em;"
id="calendar-container"&gt;&lt;/div&gt;

&lt;script type="text/javascript"&gt;
  function dateChanged(calendar) {
    // Beware that this function is called even if the end-user only
    // changed the month/year.  In order to determine if a date was
    // clicked you can use the dateClicked property of the calendar:
    if (calendar.dateClicked) {
      // OK, a date was clicked, redirect to /yyyy/mm/dd/index.php
      var y = calendar.date.getFullYear();
      var m = calendar.date.getMonth();     // integer, 0..11
      var d = calendar.date.getDate();      // integer, 1..31
      // redirect...
      window.location = "/" + y + "/" + m + "/" + d + "/index.php";
    }
  };

  Calendar.setup(
    {
      flat         : "calendar-container", // ID of the parent element
      flatCallback : dateChanged           // our callback function
    }
  );
&lt;/script&gt;</pre>

</body>
</html>
