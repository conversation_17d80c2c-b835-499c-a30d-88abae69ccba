
body { background: #fff; color: #383838; font-family: helvetica, arial, sans-serif; font-size: 15px; line-height: 145%; }
h1, h2, h3 { color: #000; }
h2, h3 { margin: 30px 0 10px 0; }
h2:first-child, h3:first-child { margin-top: 0; }
p, .p { margin: 10px 0 20px 0; }
input.time, input.date { font-size: 13px; }
input.time { width: 80px; }
input.date { width: 90px; }
a { color: #06c; text-decoration: none; }
a:hover { text-decoration: underline; }
h1 a { color: #000; }
image { border: 0; }

.example { border: 1px solid #ddd; background: #f4f4f4; padding: 10px; margin: 10px 0 30px 0; }
.example h3 { margin: 0 0 10px 0; }
.example p { margin: 20px 0 0 0; }

.big { font-size: 2em; line-height: 2em; }

.code { font-family: Consolas, Monaco, "Courier New", Courier; margin: 20px 0 0 0; background: #333; }

#container { margin: 50px auto; width: 700px; }
h1 { font-size: 3.8em; color: #000; margin: 0 0 35px 0; }
h1 .small { font-size: 0.4em; }
h1 a { text-decoration: none }
h2 { font-size: 1.5em; color: #000; }
.byline { font-size: 1.5em; font-weight: bold; margin: 20px 0 0 0; }
.byline a { text-decoration: none; }
.description { font-size: 1.2em; margin: 20px 0; font-style: italic;}

pre { background: #000; color: #fff; padding: 15px;}
hr { border: 0; width: 80%; border-bottom: 1px solid #aaa}
.footer { text-align:center; padding-top:30px; font-style: italic; }

div.ui-datepicker { font-size: 11px; }

/**
 * Blackboard theme
 *
 * Adapted from Domenico Carbotta's TextMate theme of the same name
 *
 * <AUTHOR> Carbotta
 * <AUTHOR> Campbell
 * @version 1.0.2
 */
pre {
    background: #0B1022;
    word-wrap: break-word;
    margin: 0px;
    padding: 0px;
    padding: 10px;
    color: #fff;
    font-size: 14px;
    margin-bottom: 20px;
}

pre, code {
    font-family: 'Monaco', courier, monospace;
}

pre .comment {
    color: #727272;
}

pre .constant {
    color: #D8FA3C;
}

pre .storage {
    color: #FBDE2D;
}

pre .string, pre .comment.docstring {
    color: #61CE3C;
}

pre .string.regexp, pre .support.tag.script, pre .support.tag.style {
    color: #fff;
}

pre .keyword, pre .selector {
    color: #FBDE2D;
}

pre .inherited-class {
    font-style: italic;
}

pre .entity {
    color: #FF6400;
}

pre .support, *[data-language="c"] .function.call {
    color: #8DA6CE;
}

pre .variable.global, pre .variable.class, pre .variable.instance {
    color: #FF6400;
}



/* Bootstrap datepicker CSS */
.dropdown { position:relative; }
.dropdown-toggle { *margin-bottom:-3px; }
.dropdown-toggle:active, .open .dropdown-toggle { outline:0; }
.caret {
  display:inline-block;
  width:0;
  height:0;
  text-indent:-99999px;
  *text-indent:0;
  vertical-align:top;
  border-left:4px solid transparent;
  border-right:4px solid transparent;
  border-top:4px solid #000000;
  opacity:0.3;
  filter:alpha(opacity=30);
  content:"\2193";
}
.dropdown .caret {
  margin-top:8px;
  margin-left:2px;
}
.dropdown:hover .caret, .open.dropdown .caret {
  opacity:1;
  filter:alpha(opacity=100);
}
.dropdown-menu {
  position:absolute;
  top:100%;
  left:0;
  z-index:1000;
  float:left;
  display:none;
  min-width:160px;
  max-width:220px;
  _width:160px;
  padding:4px 0;
  margin:0;
  list-style:none;
  background-color:#ffffff;
  border-color:#cccccc;
  border-color:rgba(0,0,0,0.2);
  border-style:solid;
  border-width:1px;
  -webkit-border-radius:0 0 5px 5px;
  -moz-border-radius:0 0 5px 5px;
  border-radius:0 0 5px 5px;
  -webkit-box-shadow:0 5px 10px rgba(0,0,0,0.2);
  -moz-box-shadow:0 5px 10px rgba(0,0,0,0.2);
  box-shadow:0 5px 10px rgba(0,0,0,0.2);
  -webkit-background-clip:padding-box;
  -moz-background-clip:padding;
  background-clip:padding-box;
  *border-right-width:2px;
  *border-bottom-width:2px;
}
.dropdown-menu.bottom-up {
  top:auto;
  bottom:100%;
  margin-bottom:2px;
}
.dropdown-menu .divider {
  height:1px;
  margin:5px 1px;
  overflow:hidden;
  background-color:#e5e5e5;
  border-bottom:1px solid #ffffff;
  *width:100%;
  *margin:-5px 0 5px;
}
.dropdown-menu a {
  display:block;
  padding:3px 15px;
  clear:both;
  font-weight:normal;
  line-height:18px;
  color:#555555;
  white-space:nowrap;
}
.dropdown-menu li > a:hover, .dropdown-menu .active > a, .dropdown-menu .active > a:hover {
  color:#ffffff;
  text-decoration:none;
  background-color:#0060b6;
}
.dropdown.open { *z-index:1000; }
.dropdown.open .dropdown-toggle {
  color:#ffffff;
  background:#cccccc;
  background:rgba(0,0,0,0.3);
}
.dropdown.open .dropdown-menu { display:block; }
.typeahead {
  margin-top:2px;
  -webkit-border-radius:4px;
  -moz-border-radius:4px;
  border-radius:4px;
}
/*!
 * Datepicker for Bootstrap
 *
 * Copyright 2012 Stefan Petre
 * Improvements by Andrew Rowls
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 */
.datepicker {
  top: 0;
  left: 0;
  padding: 4px;
  margin-top: 1px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  /*.dow {
    border-top: 1px solid #ddd !important;
  }*/

}
.datepicker:before {
  content: '';
  display: inline-block;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid #ccc;
  border-bottom-color: rgba(0, 0, 0, 0.2);
  position: absolute;
  top: -7px;
  left: 6px;
}
.datepicker:after {
  content: '';
  display: inline-block;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #ffffff;
  position: absolute;
  top: -6px;
  left: 7px;
}
.datepicker > div {
  display: none;
}
.datepicker.days div.datepicker-days {
  display: block;
}
.datepicker.months div.datepicker-months {
  display: block;
}
.datepicker.years div.datepicker-years {
  display: block;
}
.datepicker table {
  margin: 0;
}
.datepicker td,
.datepicker th {
  text-align: center;
  width: 20px;
  height: 20px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.datepicker td.day:hover {
  background: #eeeeee;
  cursor: pointer;
}
.datepicker td.old,
.datepicker td.new {
  color: #999999;
}
.datepicker td.disabled,
.datepicker td.disabled:hover {
  background: none;
  color: #999999;
  cursor: default;
}
.datepicker td.today,
.datepicker td.today:hover,
.datepicker td.today.disabled,
.datepicker td.today.disabled:hover {
  background-color: #fde19a;
  background-image: -moz-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: -ms-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#fdd49a), to(#fdf59a));
  background-image: -webkit-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: -o-linear-gradient(top, #fdd49a, #fdf59a);
  background-image: linear-gradient(top, #fdd49a, #fdf59a);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fdd49a', endColorstr='#fdf59a', GradientType=0);
  border-color: #fdf59a #fdf59a #fbed50;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:dximagetransform.microsoft.gradient(enabled=false);
}
.datepicker td.today:hover,
.datepicker td.today:hover:hover,
.datepicker td.today.disabled:hover,
.datepicker td.today.disabled:hover:hover,
.datepicker td.today:active,
.datepicker td.today:hover:active,
.datepicker td.today.disabled:active,
.datepicker td.today.disabled:hover:active,
.datepicker td.today.active,
.datepicker td.today:hover.active,
.datepicker td.today.disabled.active,
.datepicker td.today.disabled:hover.active,
.datepicker td.today.disabled,
.datepicker td.today:hover.disabled,
.datepicker td.today.disabled.disabled,
.datepicker td.today.disabled:hover.disabled,
.datepicker td.today[disabled],
.datepicker td.today:hover[disabled],
.datepicker td.today.disabled[disabled],
.datepicker td.today.disabled:hover[disabled] {
  background-color: #fdf59a;
}
.datepicker td.today:active,
.datepicker td.today:hover:active,
.datepicker td.today.disabled:active,
.datepicker td.today.disabled:hover:active,
.datepicker td.today.active,
.datepicker td.today:hover.active,
.datepicker td.today.disabled.active,
.datepicker td.today.disabled:hover.active {
  background-color: #fbf069 \9;
}
.datepicker td.active,
.datepicker td.active:hover,
.datepicker td.active.disabled,
.datepicker td.active.disabled:hover {
  background-color: #006dcc;
  background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
  background-image: -ms-linear-gradient(top, #0088cc, #0044cc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
  background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
  background-image: -o-linear-gradient(top, #0088cc, #0044cc);
  background-image: linear-gradient(top, #0088cc, #0044cc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0088cc', endColorstr='#0044cc', GradientType=0);
  border-color: #0044cc #0044cc #002a80;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:dximagetransform.microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}
.datepicker td.active:hover,
.datepicker td.active:hover:hover,
.datepicker td.active.disabled:hover,
.datepicker td.active.disabled:hover:hover,
.datepicker td.active:active,
.datepicker td.active:hover:active,
.datepicker td.active.disabled:active,
.datepicker td.active.disabled:hover:active,
.datepicker td.active.active,
.datepicker td.active:hover.active,
.datepicker td.active.disabled.active,
.datepicker td.active.disabled:hover.active,
.datepicker td.active.disabled,
.datepicker td.active:hover.disabled,
.datepicker td.active.disabled.disabled,
.datepicker td.active.disabled:hover.disabled,
.datepicker td.active[disabled],
.datepicker td.active:hover[disabled],
.datepicker td.active.disabled[disabled],
.datepicker td.active.disabled:hover[disabled] {
  background-color: #0044cc;
}
.datepicker td.active:active,
.datepicker td.active:hover:active,
.datepicker td.active.disabled:active,
.datepicker td.active.disabled:hover:active,
.datepicker td.active.active,
.datepicker td.active:hover.active,
.datepicker td.active.disabled.active,
.datepicker td.active.disabled:hover.active {
  background-color: #003399 \9;
}
.datepicker td span {
  display: block;
  width: 23%;
  height: 54px;
  line-height: 54px;
  float: left;
  margin: 1%;
  cursor: pointer;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.datepicker td span:hover {
  background: #eeeeee;
}
.datepicker td span.disabled,
.datepicker td span.disabled:hover {
  background: none;
  color: #999999;
  cursor: default;
}
.datepicker td span.active,
.datepicker td span.active:hover,
.datepicker td span.active.disabled,
.datepicker td span.active.disabled:hover {
  background-color: #006dcc;
  background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
  background-image: -ms-linear-gradient(top, #0088cc, #0044cc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
  background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
  background-image: -o-linear-gradient(top, #0088cc, #0044cc);
  background-image: linear-gradient(top, #0088cc, #0044cc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0088cc', endColorstr='#0044cc', GradientType=0);
  border-color: #0044cc #0044cc #002a80;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  filter: progid:dximagetransform.microsoft.gradient(enabled=false);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}
.datepicker td span.active:hover,
.datepicker td span.active:hover:hover,
.datepicker td span.active.disabled:hover,
.datepicker td span.active.disabled:hover:hover,
.datepicker td span.active:active,
.datepicker td span.active:hover:active,
.datepicker td span.active.disabled:active,
.datepicker td span.active.disabled:hover:active,
.datepicker td span.active.active,
.datepicker td span.active:hover.active,
.datepicker td span.active.disabled.active,
.datepicker td span.active.disabled:hover.active,
.datepicker td span.active.disabled,
.datepicker td span.active:hover.disabled,
.datepicker td span.active.disabled.disabled,
.datepicker td span.active.disabled:hover.disabled,
.datepicker td span.active[disabled],
.datepicker td span.active:hover[disabled],
.datepicker td span.active.disabled[disabled],
.datepicker td span.active.disabled:hover[disabled] {
  background-color: #0044cc;
}
.datepicker td span.active:active,
.datepicker td span.active:hover:active,
.datepicker td span.active.disabled:active,
.datepicker td span.active.disabled:hover:active,
.datepicker td span.active.active,
.datepicker td span.active:hover.active,
.datepicker td span.active.disabled.active,
.datepicker td span.active.disabled:hover.active {
  background-color: #003399 \9;
}
.datepicker td span.old {
  color: #999999;
}
.datepicker th.switch {
  width: 145px;
}
.datepicker thead tr:first-child th,
.datepicker tfoot tr:first-child th {
  cursor: pointer;
}
.datepicker thead tr:first-child th:hover,
.datepicker tfoot tr:first-child th:hover {
  background: #eeeeee;
}
.input-append.date .add-on i,
.input-prepend.date .add-on i {
  display: block;
  cursor: pointer;
  width: 16px;
  height: 16px;
}

[class^="icon-"], [class*=" icon-"] {
  display:inline-block;
  width:14px;
  height:14px;
  *margin-right:.3em;
  line-height:14px;
  vertical-align:text-top;
  background-image:url("glyphicons-halflings.png");
  background-position:14px 14px;
  background-repeat:no-repeat;
}
[class^="icon-"]:last-child, [class*=" icon-"]:last-child { *margin-left:0; }
.icon-arrow-left { background-position:-240px -96px; }
.icon-arrow-right { background-position:-264px -96px; }