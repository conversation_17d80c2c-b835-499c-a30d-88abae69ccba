<?php
/**
 *	\file       rendezvousclient/install_missing_tables.php
 *	\ingroup    rendezvousclient
 *	\brief      Installation des tables manquantes
 */

require_once '../../main.inc.php';

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'admin'));

// Sécurité
if (!$user->admin) {
    accessforbidden();
}

$action = GETPOST('action', 'aZ09');

/*
 * Actions
 */

if ($action == 'install_missing_tables') {
    $error = 0;
    $tables_created = 0;
    
    // Définition des requêtes SQL pour créer les tables manquantes
    $sql_queries = array();
    
    // Table principale des rendez-vous
    $sql_queries[] = "CREATE TABLE IF NOT EXISTS ".MAIN_DB_PREFIX."rendez_vous (
        rowid integer AUTO_INCREMENT PRIMARY KEY,
        fk_projet integer NOT NULL,
        fk_statut tinyint DEFAULT 1,
        type tinyint DEFAULT 1,
        numero varchar(50),
        date datetime,
        date_demo datetime,
        date_livraison datetime,
        objet varchar(255),
        objectif_client text,
        besoin_client text,
        reponse_besoin_client text,
        compte_rendu text,
        datec datetime,
        fk_user integer,
        INDEX idx_fk_projet (fk_projet),
        INDEX idx_fk_statut (fk_statut),
        INDEX idx_date (date)
    ) ENGINE=innodb";
    
    // Liaison rendez-vous / contacts
    $sql_queries[] = "CREATE TABLE IF NOT EXISTS ".MAIN_DB_PREFIX."rendez_vous_socpeople (
        rowid integer AUTO_INCREMENT PRIMARY KEY,
        fk_rendez_vous integer NOT NULL,
        fk_socpeople integer NOT NULL,
        INDEX idx_fk_rendez_vous (fk_rendez_vous),
        INDEX idx_fk_socpeople (fk_socpeople)
    ) ENGINE=innodb";
    
    // Sites clients
    $sql_queries[] = "CREATE TABLE IF NOT EXISTS ".MAIN_DB_PREFIX."rendez_vous_site (
        rowid integer AUTO_INCREMENT PRIMARY KEY,
        fk_projet integer NOT NULL,
        date datetime,
        nom varchar(255),
        type varchar(100),
        description text,
        nombre_utilisateur integer,
        fk_logiciel integer,
        autre text,
        hebergement text,
        INDEX idx_fk_projet (fk_projet)
    ) ENGINE=innodb";
    
    // Types d'utilisateurs par site
    $sql_queries[] = "CREATE TABLE IF NOT EXISTS ".MAIN_DB_PREFIX."rendez_vous_site_utilisateur (
        rowid integer AUTO_INCREMENT PRIMARY KEY,
        fk_rendez_vous_site integer NOT NULL,
        type varchar(100),
        descriptif text,
        INDEX idx_fk_site (fk_rendez_vous_site)
    ) ENGINE=innodb";
    
    // Modules sélectionnés par site
    $sql_queries[] = "CREATE TABLE IF NOT EXISTS ".MAIN_DB_PREFIX."rendez_vous_site_module (
        rowid integer AUTO_INCREMENT PRIMARY KEY,
        fk_site integer NOT NULL,
        fk_module integer NOT NULL,
        checked tinyint DEFAULT 0,
        INDEX idx_fk_site (fk_site),
        INDEX idx_fk_module (fk_module)
    ) ENGINE=innodb";
    
    // Constantes par module de site
    $sql_queries[] = "CREATE TABLE IF NOT EXISTS ".MAIN_DB_PREFIX."rendez_vous_site_module_constante (
        rowid integer AUTO_INCREMENT PRIMARY KEY,
        fk_site_module integer NOT NULL,
        fk_constante integer NOT NULL,
        checked tinyint DEFAULT 0,
        INDEX idx_fk_site_module (fk_site_module)
    ) ENGINE=innodb";
    
    // Développements spécifiques
    $sql_queries[] = "CREATE TABLE IF NOT EXISTS ".MAIN_DB_PREFIX."rendez_vous_site_module_devspe (
        rowid integer AUTO_INCREMENT PRIMARY KEY,
        fk_site_module integer NOT NULL,
        description text,
        INDEX idx_fk_site_module (fk_site_module)
    ) ENGINE=innodb";
    
    // Paramétrages par module
    $sql_queries[] = "CREATE TABLE IF NOT EXISTS ".MAIN_DB_PREFIX."rendez_vous_site_module_param (
        rowid integer AUTO_INCREMENT PRIMARY KEY,
        fk_site_module integer NOT NULL,
        description text,
        INDEX idx_fk_site_module (fk_site_module)
    ) ENGINE=innodb";
    
    // Champs supplémentaires
    $sql_queries[] = "CREATE TABLE IF NOT EXISTS ".MAIN_DB_PREFIX."rendez_vous_site_module_extrafields (
        rowid integer AUTO_INCREMENT PRIMARY KEY,
        fk_site_module integer NOT NULL,
        description text,
        INDEX idx_fk_site_module (fk_site_module)
    ) ENGINE=innodb";
    
    // Cahier des charges principal
    $sql_queries[] = "CREATE TABLE IF NOT EXISTS ".MAIN_DB_PREFIX."rendez_vous_cahier_des_charges (
        rowid integer AUTO_INCREMENT PRIMARY KEY,
        fk_projet integer NOT NULL,
        version varchar(10),
        date_creation datetime,
        date_demo datetime,
        date_livraison datetime,
        nb_rdv_amont varchar(100),
        nb_rdv_aval varchar(100),
        nb_jours_formations varchar(100),
        intro_contexte_projet text,
        intro_objectifs_globaux text,
        intro_presentation_client text,
        perimetre_projet_delimitation text,
        perimetre_projet_def_processus text,
        besoins_processus_par_site text,
        besoins_fonctionnels_user text,
        architecture_solutions text,
        architecture_modules text,
        architecture_infrastructure text,
        process_av_vente text,
        flux_logistiques text,
        prod_et_qualite text,
        commandes_et_expeditions text,
        creation_recettes text,
        reporting_analyse text,
        ux_roles text,
        ux_interfaces text,
        ux_kpi_client text,
        formation_programme text,
        formation_documentation text,
        maintenance_support text,
        maintenance_mises_a_jour text,
        deploiement_calendrier text,
        deploiement_jalons text,
        succes_kpi text,
        succes_suivi text,
        budget_previsionnel text,
        modifs_recommandations text,
        mentions_confidentialite text,
        mentions_propriete text,
        mentions_conditions_modification text,
        INDEX idx_fk_projet (fk_projet)
    ) ENGINE=innodb";
    
    // Logiciels disponibles
    $sql_queries[] = "CREATE TABLE IF NOT EXISTS ".MAIN_DB_PREFIX."avimm_constante_logiciel (
        rowid integer AUTO_INCREMENT PRIMARY KEY,
        libelle varchar(255) NOT NULL,
        description text,
        cahier_des_charges text,
        tarif decimal(24,8) DEFAULT 0,
        active tinyint DEFAULT 1
    ) ENGINE=innodb";
    
    // Modules disponibles
    $sql_queries[] = "CREATE TABLE IF NOT EXISTS ".MAIN_DB_PREFIX."avimm_constante_module (
        rowid integer AUTO_INCREMENT PRIMARY KEY,
        libelle varchar(255) NOT NULL,
        fk_logiciel integer NOT NULL,
        constante text,
        tarif decimal(24,8) DEFAULT 0,
        cahier_des_charges text,
        synthese_cdc text,
        active tinyint DEFAULT 1,
        INDEX idx_fk_logiciel (fk_logiciel)
    ) ENGINE=innodb";
    
    // Constantes système
    $sql_queries[] = "CREATE TABLE IF NOT EXISTS ".MAIN_DB_PREFIX."avimm_constante (
        rowid integer AUTO_INCREMENT PRIMARY KEY,
        libelle varchar(255) NOT NULL,
        valeur text,
        dependance_sql text,
        description text,
        debase tinyint DEFAULT 0,
        cahier_des_charges text,
        tarif decimal(24,8) DEFAULT 0,
        active tinyint DEFAULT 1
    ) ENGINE=innodb";
    
    // Liaison constantes/modules
    $sql_queries[] = "CREATE TABLE IF NOT EXISTS ".MAIN_DB_PREFIX."avimm_constante_inmodule (
        rowid integer AUTO_INCREMENT PRIMARY KEY,
        fk_constante integer NOT NULL,
        fk_module integer NOT NULL,
        INDEX idx_fk_constante (fk_constante),
        INDEX idx_fk_module (fk_module)
    ) ENGINE=innodb";
    
    // Exécution des requêtes
    foreach ($sql_queries as $sql) {
        $resql = $db->query($sql);
        if ($resql) {
            $tables_created++;
        } else {
            $error++;
            setEventMessages("Erreur SQL: ".$db->lasterror(), null, 'errors');
        }
    }
    
    // Insertion des données de base
    if ($error == 0) {
        // Logiciels par défaut
        $insert_queries = array();
        
        $insert_queries[] = "INSERT IGNORE INTO ".MAIN_DB_PREFIX."avimm_constante_logiciel (libelle, description, cahier_des_charges) VALUES
            ('Dolibarr', 'ERP/CRM Dolibarr', 'Solution ERP/CRM open source complète avec gestion commerciale, comptabilité, stocks, projets, etc.'),
            ('VTiger', 'CRM VTiger', 'Solution CRM open source avec gestion des contacts, opportunités, campagnes marketing.'),
            ('WordPress', 'CMS WordPress', 'Système de gestion de contenu web flexible et extensible.'),
            ('PrestaShop', 'E-commerce PrestaShop', 'Solution e-commerce open source avec gestion catalogue, commandes, paiements.'),
            ('Magento', 'E-commerce Magento', 'Plateforme e-commerce avancée pour sites marchands complexes.'),
            ('Autre', 'Autre logiciel', 'Autre solution logicielle selon les besoins spécifiques.')";
        
        $insert_queries[] = "INSERT IGNORE INTO ".MAIN_DB_PREFIX."avimm_constante_module (libelle, fk_logiciel, synthese_cdc, cahier_des_charges) VALUES
            ('Sociétés/Contacts', 1, 'Gestion complète des tiers (clients, prospects, fournisseurs) avec fiches détaillées, contacts, historique des interactions.', 'Module de base pour la gestion des relations clients et fournisseurs.'),
            ('Factures clients', 1, 'Création et gestion des factures clients avec modèles personnalisables, relances automatiques, suivi des paiements.', 'Facturation complète avec gestion des échéances et relances.'),
            ('Commandes clients', 1, 'Gestion du cycle de vente complet depuis le devis jusqu''à la livraison avec workflow personnalisable.', 'Processus commercial intégré avec validation et suivi.'),
            ('Projets', 1, 'Gestion de projets avec tâches, temps passé, jalons, budget et facturation au temps passé.', 'Module projet complet avec gestion des ressources.'),
            ('Produits/Services', 1, 'Catalogue produits avec variantes, prix, stocks, fournisseurs et codes-barres.', 'Gestion complète du référentiel produits.'),
            ('Stocks', 1, 'Gestion des stocks multi-entrepôts avec mouvements, inventaires et valorisation.', 'Logistique et gestion des stocks avancée.')";
        
        foreach ($insert_queries as $sql) {
            $resql = $db->query($sql);
            if (!$resql) {
                $error++;
                setEventMessages("Erreur insertion données: ".$db->lasterror(), null, 'errors');
            }
        }
    }
    
    if ($error == 0) {
        setEventMessages("Installation terminée avec succès. ".$tables_created." tables créées/vérifiées.", null, 'mesgs');
    } else {
        setEventMessages("Installation terminée avec ".$error." erreur(s).", null, 'warnings');
    }
}

/*
 * View
 */

$title = 'Installation des tables manquantes';
llxHeader('', $title);

print load_fiche_titre($title, '', 'setup');

print '<div class="info">';
print 'Ce script va créer toutes les tables manquantes pour le module Rendez-vous Client.';
print '</div>';

// Bouton d'installation
print '<div class="center" style="margin: 20px 0;">';
print '<a class="butAction" href="'.$_SERVER['PHP_SELF'].'?action=install_missing_tables&token='.newToken().'">';
print 'Installer les tables manquantes';
print '</a>';
print '</div>';

// Vérification des tables avant installation
print '<h3>État actuel des tables</h3>';

$tables_to_check = array(
    'rendez_vous' => 'Table principale des rendez-vous',
    'rendez_vous_socpeople' => 'Liaison rendez-vous / contacts',
    'rendez_vous_site' => 'Sites clients',
    'rendez_vous_site_utilisateur' => 'Types d\'utilisateurs par site',
    'rendez_vous_site_module' => 'Modules sélectionnés par site',
    'rendez_vous_site_module_constante' => 'Constantes par module de site',
    'rendez_vous_site_module_devspe' => 'Développements spécifiques',
    'rendez_vous_site_module_param' => 'Paramétrages par module',
    'rendez_vous_site_module_extrafields' => 'Champs supplémentaires',
    'rendez_vous_synthese_cdc' => 'Synthèse du cahier des charges',
    'rendez_vous_demo' => 'Démos créées',
    'rendez_vous_cahier_des_charges' => 'Cahier des charges principal',
    'avimm_constante_logiciel' => 'Logiciels disponibles',
    'avimm_constante_module' => 'Modules disponibles',
    'avimm_constante' => 'Constantes système',
    'avimm_constante_inmodule' => 'Liaison constantes/modules'
);

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<th>Table</th>';
print '<th>Statut</th>';
print '<th>Nombre d\'enregistrements</th>';
print '<th>Description</th>';
print '</tr>';

$tables_missing = 0;
foreach ($tables_to_check as $table => $description) {
    $sql = "SHOW TABLES LIKE '".MAIN_DB_PREFIX.$table."'";
    $resql = $db->query($sql);
    
    print '<tr class="oddeven">';
    print '<td><strong>'.MAIN_DB_PREFIX.$table.'</strong></td>';
    
    if ($resql && $db->num_rows($resql) > 0) {
        print '<td><span class="badge badge-status4 badge-status">Existe</span></td>';
        
        // Compter les enregistrements
        $sql_count = "SELECT COUNT(*) as nb FROM ".MAIN_DB_PREFIX.$table;
        $resql_count = $db->query($sql_count);
        if ($resql_count) {
            $obj_count = $db->fetch_object($resql_count);
            print '<td>'.$obj_count->nb.' enregistrement(s)</td>';
        } else {
            print '<td>-</td>';
        }
    } else {
        print '<td><span class="badge badge-status8 badge-status">Manquante</span></td>';
        print '<td>-</td>';
        $tables_missing++;
    }
    
    print '<td>'.$description.'</td>';
    print '</tr>';
}

print '</table>';

// Résumé
print '<br>';
if ($tables_missing == 0) {
    print '<div class="ok">';
    print '<strong>Parfait !</strong> Toutes les tables sont présentes.';
    print '</div>';
} else {
    print '<div class="warning">';
    print '<strong>Attention !</strong> '.$tables_missing.' table(s) manquante(s) sur '.count($tables_to_check).' tables nécessaires.';
    print '</div>';
}

// Liens utiles
print '<br><h3>Liens utiles</h3>';
print '<div class="tabsAction">';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_installation.php">Test complet</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/test_forms.php">Test des formulaires</a>';
print '<a class="butAction" href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rendezvousclientindex.php">Accueil du module</a>';
print '</div>';

// End of page
llxFooter();
$db->close();
