<?php
/**
 *	\file       rendezvousclient/rdv/list.php
 *	\ingroup    rendezvousclient
 *	\brief      page de la liste des rendez-vous
 */

require_once '../../../main.inc.php';
dol_include_once('/rendezvousclient/rdv/class/rendezvous.class.php');
require_once DOL_DOCUMENT_ROOT.'/societe/class/societe.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';

$langs->loadLangs(array("rendezvousclient"));

$search_id = trim(GETPOST("search_id", 'int'));
$search_nom = trim(GETPOST("search_nom", 'restricthtml'));
$search_numero = trim(GETPOST("search_numero", 'alpha'));
$search_date_start = dol_mktime(0, 0, 0, GETPOST('search_date_start_month', 'int'), GETPOST('search_date_start_day', 'int'), GETPOST('search_date_start_year', 'int'));
$search_date_end = dol_mktime(23, 59, 59, GETPOST('search_date_end_month', 'int'), GETPOST('search_date_end_day', 'int'), GETPOST('search_date_end_year', 'int'));
$search_date_demo_start = dol_mktime(0, 0, 0, GETPOST('search_date_demo_start_month', 'int'), GETPOST('search_date_demo_start_day', 'int'), GETPOST('search_date_demo_start_year', 'int'));
$search_date_demo_end = dol_mktime(23, 59, 59, GETPOST('search_date_demo_end_month', 'int'), GETPOST('search_date_demo_end_day', 'int'), GETPOST('search_date_demo_end_year', 'int'));
$search_date_livraison_start = dol_mktime(0, 0, 0, GETPOST('search_date_livraison_start_month', 'int'), GETPOST('search_date_livraison_start_day', 'int'), GETPOST('search_date_livraison_start_year', 'int'));
$search_date_livraison_end = dol_mktime(23, 59, 59, GETPOST('search_date_livraison_end_month', 'int'), GETPOST('search_date_livraison_end_day', 'int'), GETPOST('search_date_livraison_end_year', 'int'));
$search_objet = trim(GETPOST('search_objet', 'restricthtml'));
$search_compte_rendu = trim(GETPOST('search_compte_rendu', 'restricthtml'));
$search_statut = trim(GETPOST('search_statut', 'alpha'));
$search_datec_start = dol_mktime(0, 0, 0, GETPOST('search_datec_start_month', 'int'), GETPOST('search_datec_start_day', 'int'), GETPOST('search_datec_start_year', 'int'));
$search_datec_end = dol_mktime(23, 59, 59, GETPOST('search_datec_end_month', 'int'), GETPOST('search_datec_end_day', 'int'), GETPOST('search_datec_end_year', 'int'));

$limit = GETPOST('limit', 'int') ?GETPOST('limit', 'int') : $conf->liste_limit;
$sortfield = GETPOST('sortfield', 'aZ09comma');
$sortorder = GETPOST('sortorder', 'aZ09comma');
$page = GETPOSTISSET('pageplusone') ? (GETPOST('pageplusone') - 1) : GETPOST("page", 'int');
if (!$sortorder) {
	$sortorder = "ASC, ASC";
}
if (!$sortfield) {
	$sortfield = "s.nom, r.numero";
}
if (empty($page) || $page < 0 || GETPOST('button_search', 'alpha') || GETPOST('button_removefilter', 'alpha')) {
	$page = 0;
}     // If $page is not defined, or '' or -1 or if we click on clear filters or if we select empty mass action
$offset = $limit * $page;
$pageprev = $page - 1;
$pagenext = $page + 1;

$object = new Rendezvous($db);

$societe = new Societe($db);

$arrayfields = array(
	'r.rowid'=>array('label'=>"TechnicalID", 'position'=>1, 'checked'=>(!empty($conf->global->MAIN_SHOW_TECHNICAL_ID)), 'enabled'=>(!empty($conf->global->MAIN_SHOW_TECHNICAL_ID))),
	's.nom'=>array('label'=>"ThirdPartyName", 'position'=>2, 'checked'=>1),
	'r.numero'=>array('label'=>"NumeroRDV", 'position'=>3, 'checked'=>1),
	'r.date'=>array('label'=>"Date", 'position'=>4, 'checked'=>1),
	'r.date_demo'=>array('label'=>"DateD", 'position'=>5, 'checked'=>0),
	'r.date_livraison'=>array('label'=>"DATE_LIVRAISON", 'position'=>6, 'checked'=>0),
	'r.objet'=>array('label'=>"ObjRDV", 'position'=>7, 'checked'=>1),
	'r.compte_rendu'=>array('label'=>"CompteRendu", 'position'=>8, 'checked'=>1),
	'r.fk_statut'=>array('label'=>"Statut", 'position'=>9, 'checked'=>1),
	'r.datec'=>array('label'=>"DateCreation", 'position'=>10, 'checked'=>0)
);

/*
 * Actions
*/
if (empty($reshook)) {
	// Selection of new fields
	include DOL_DOCUMENT_ROOT.'/core/actions_changeselectedfields.inc.php';

	// Did we click on purge search criteria ?
	if (GETPOST('button_removefilter_x', 'alpha') || GETPOST('button_removefilter.x', 'alpha') || GETPOST('button_removefilter', 'alpha')) { // All tests are required to be compatible with all browsers
		$search_nom = '';
		$search_numero = '';
		$search_date_start = '';
		$search_date_end = '';
		$search_date_end = '';
		$search_date_demo_start = '';
		$search_date_demo_end = '';
		$search_date_livraison_start = '';
		$search_date_livraison_end = '';
		$search_objet = '';
		$search_compte_rendu = '';
		$search_statut = '';
		$search_datec_start = '';
		$search_datec_end = '';
		$sortorder = "ASC, ASC";
		$sortfield = "s.nom, r.numero";
	}
}

/*
 * View
*/

$form = new Form($db);

$sql = "SELECT r.rowid, s.nom, p.fk_soc, r.numero, r.date, r.date_demo, r.date_livraison, r.objet, r.compte_rendu, r.fk_statut, r.datec";
$sql .= " FROM ".MAIN_DB_PREFIX."rendez_vous r";
$sql .= " INNER JOIN ".MAIN_DB_PREFIX."projet p ON p.rowid = r.fk_projet";
$sql .= " INNER JOIN ".MAIN_DB_PREFIX."societe s ON s.rowid = p.fk_soc";
$sql .= " WHERE 1=1";
if($search_id){
	$sql .= natural_search('r.rowid', $search_id);
}
if($search_nom){
	$sql .= natural_search('s.nom', $search_nom);
}
if($search_numero){
	$sql .= natural_search('r.numero', $search_numero);
}
if($search_date_start){
	$sql .= " AND r.date >= '".$db->idate($search_date_start)."'";
}
if($search_date_end){
	$sql .= " AND r.date <= '".$db->idate($search_date_end)."'";
}
if($search_date_demo_start){
	$sql .= " AND r.date_demo >= '".$db->idate($search_date_demo_start)."'";
}
if($search_date_demo_end){
	$sql .= " AND r.date_demo <= '".$db->idate($search_date_demo_end)."'";
}
if($search_date_livraison_start){
	$sql .= " AND r.date_livraison >= '".$db->idate($search_date_livraison_start)."'";
}
if($search_date_livraison_end){
	$sql .= " AND r.date_livraison <= '".$db->idate($search_date_livraison_end)."'";
}
if($search_objet){
	$sql .= natural_search('r.objet', $search_objet);
}
if($search_compte_rendu){
	$sql .= natural_search('r.compte_rendu', $search_compte_rendu);
}
if($search_statut >= 0 && $search_statut != ''){
	$sql .= " AND r.fk_statut IN (".$search_statut.")";
}
if($search_datec_start){
	$sql .= " AND r.datec >= '".$db->idate($search_date_start)."'";
}
if($search_datec_end){
	$sql .= " AND r.datec <= '".$db->idate($search_date_end)."'";
}

// Count total nb of records with no order and no limits
$nbtotalofrecords = '';
if (empty($conf->global->MAIN_DISABLE_FULL_SCANLIST)) {
	$resql = $db->query($sql);
	if ($resql) {
		$nbtotalofrecords = $db->num_rows($resql);
	} else {
		dol_print_error($db);
	}

	if (($page * $limit) > $nbtotalofrecords) {	// if total resultset is smaller then paging size (filtering), goto and load page 0
		$page = 0;
		$offset = 0;
	}
	$db->free($resql);
}

// Complete request and execute it with limit
$sql .= $db->order($sortfield, $sortorder);
if ($limit) {
	$sql .= $db->plimit($limit + 1, $offset);
}

$resql = $db->query($sql);
if (!$resql) {
	dol_print_error($db);
	exit;
}

$num = $db->num_rows($resql);

$title = $langs->trans("RDVlong");
llxHeader('', $title, '');

$param = '';
if ($limit > 0 && $limit != $conf->liste_limit) {
	$param .= '&limit='.urlencode($limit);
}
if($search_id){
	$param .= '&search_id='.urlencode($search_id);
}
if($search_nom){
	$param .= '&search_nom='.urlencode($search_nom);
}
if($search_numero){
	$param .= '&search_numero='.urlencode($search_numero);
}
if($search_date_start){
	$param .= '&search_date_startday='.dol_print_date($search_date_start, '%d').'&search_date_startmonth='.dol_print_date($search_date_start, '%m').'&search_date_startyear='.dol_print_date($search_date_start, '%Y');
}
if($search_date_end){
	$param .= '&search_date_end='.dol_print_date($search_date_end, '%d').'&search_date_endmonth='.dol_print_date($search_date_end, '%m').'&ssearch_date_endyear='.dol_print_date($search_date_end, '%Y');
}
if($search_date_demo_start){
	$param .= '&search_date_demo_startday='.dol_print_date($search_date_demo_start, '%d').'&search_date_demo_startmonth='.dol_print_date($search_date_demo_start, '%m').'&search_date_demo_startyear='.dol_print_date($search_date_demo_start, '%Y');
}
if($search_date_demo_end){
	$param .= '&search_date_demo_end='.dol_print_date($search_date_demo_end, '%d').'&search_date_demo_endmonth='.dol_print_date($search_date_demo_end, '%m').'&ssearch_date_demo_endyear='.dol_print_date($search_date_demo_end, '%Y');
}
if($search_date_livraison_start){
	$param .= '&search_date_livraison_startday='.dol_print_date($search_date_livraison_start, '%d').'&search_date_livraison_startmonth='.dol_print_date($search_date_livraison_start, '%m').'&search_date_livraison_startyear='.dol_print_date($search_date_livraison_start, '%Y');
}
if($search_date_livraison_end){
	$param .= '&search_date_livraison_end='.dol_print_date($search_date_livraison_end, '%d').'&search_date_livraison_endmonth='.dol_print_date($search_date_livraison_end, '%m').'&ssearch_date_livraison_endyear='.dol_print_date($search_date_livraison_end, '%Y');
}
if($search_objet){
	$param .= '&search_objet='.urlencode($search_objet);
}
if($search_compte_rendu){
	$param .= '&search_compte_rendu='.urlencode($search_compte_rendu);
}
if($search_statut >= 0 && $search_statut != ''){
	$param .= '&search_statut='.urlencode($search_statut);
}
if($search_datec_start){
	$param .= '&search_datec_startday='.dol_print_date($search_datec_start, '%d').'&search_datec_startmonth='.dol_print_date($search_datec_start, '%m').'&search_datec_startyear='.dol_print_date($search_datec_start, '%Y');
}
if($search_datec_end){
	$param .= '&search_datec_end='.dol_print_date($search_datec_end, '%d').'&search_datec_endmonth='.dol_print_date($search_datec_end, '%m').'&ssearch_datec_endyear='.dol_print_date($search_datec_end, '%Y');
}

$url = DOL_URL_ROOT.'/custom/rendezvousclient/rdv/card.php?action=create';

$newcardbutton = dolGetButtonTitle($langs->trans('NewRDVClient'), '', 'fa fa-plus-circle', $url, '', 1);

// Lines of title fields
print '<form method="POST" id="searchFormList" action="'.$_SERVER["PHP_SELF"].'">';
print '<input type="hidden" name="token" value="'.newToken().'">';
print '<input type="hidden" name="formfilteraction" id="formfilteraction" value="list">';
print '<input type="hidden" name="action" value="list">';
print '<input type="hidden" name="sortfield" value="'.$sortfield.'">';
print '<input type="hidden" name="sortorder" value="'.$sortorder.'">';

print_barre_liste($title, $page, $_SERVER["PHP_SELF"], $param, $sortfield, $sortorder, '', $num, $nbtotalofrecords, 'order', 0, $newcardbutton, '', $limit, 0, 0, 1);

$varpage = empty($contextpage) ? $_SERVER["PHP_SELF"] : $contextpage;
$selectedfields = $form->multiSelectArrayWithCheckbox('selectedfields', $arrayfields, $varpage); // This also change content of $arrayfields
// $selectedfields .= $form->showCheckAddButtons('checkforselect', 1);

if(!empty($conf->global->MAIN_AVIMM_STICKY_ENTETELISTE)){
	print '<div class="div-table-responsive" style="overflow-x: unset">';
}else{
	print '<div class="div-table-responsive">';
}
print '<table class="tagtable liste">'."\n";

if(!empty($conf->global->MAIN_AVIMM_STICKY_ENTETELISTE)){
	print '<tr class="liste_titre_filter" id="thirdsticky" style="position: sticky; z-index: 1000;">';
	print '<script>
	$(document).ready(function(){
		var secondsticky = $("#secondsticky").outerHeight(true);
		if(!$.isNumeric(secondsticky)){
			secondsticky = 0;
		}
		var top = $("#id-top").outerHeight(true) + secondsticky;
		$("#thirdsticky").css("top", Math.floor(top)+"px");
	});
	</script>';
}else{
	print '<tr class="liste_titre_filter">';
}

if(!empty($arrayfields['r.rowid']['checked'])){
	print '<td class="liste_titre">';
	print '<input class="flat" size="11" maxlength="11" type="text" name="search_id" value="'.$search_id.'">';
	print '</td>';
}
if(!empty($arrayfields['s.nom']['checked'])){
	print '<td class="liste_titre">';
	print '<input class="flat" size="40" maxlength="128" type="text" name="search_nom" value="'.$search_nom.'">';
	print '</td>';
}
if(!empty($arrayfields['r.numero']['checked'])){
	print '<td class="liste_titre">';
	print '<input class="flat" size="20" maxlength="11" type="text" name="search_numero" value="'.$search_numero.'">';
	print '</td>';
}
if(!empty($arrayfields['r.date']['checked'])){
	print '<td class="liste_titre center">';
	print '<div class="nowrap">';
	print $form->selectDate($search_date_start ? $search_date_start : -1, 'search_date_start_', 0, 0, 1, '', 1, 0, 0, '', '', '', '', 1, '', $langs->trans('From'));
	print '</div>';
	print '<div class="nowrap">';
	print $form->selectDate($search_date_end ? $search_date_end : -1, 'search_date_end_', 0, 0, 1, '', 1, 0, 0, '', '', '', '', 1, '', $langs->trans('to'));
	print '</div>';
	print '</td>';
}
if(!empty($arrayfields['r.date_demo']['checked'])){
	print '<td class="liste_titre center">';
	print '<div class="nowrap">';
	print $form->selectDate($search_date_demo_start ? $search_date_demo_start : -1, 'search_date_demo_start_', 0, 0, 1, '', 1, 0, 0, '', '', '', '', 1, '', $langs->trans('From'));
	print '</div>';
	print '<div class="nowrap">';
	print $form->selectDate($search_date_demo_end ? $search_date_demo_end : -1, 'search_date_demo_end_', 0, 0, 1, '', 1, 0, 0, '', '', '', '', 1, '', $langs->trans('to'));
	print '</div>';
	print '</td>';
}
if(!empty($arrayfields['r.date_livraison']['checked'])){
	print '<td class="liste_titre center">';
	print '<div class="nowrap">';
	print $form->selectDate($search_date_livraison_start ? $search_date_livraison_start : -1, 'search_date_livraison_start_', 0, 0, 1, '', 1, 0, 0, '', '', '', '', 1, '', $langs->trans('From'));
	print '</div>';
	print '<div class="nowrap">';
	print $form->selectDate($search_date_livraison_end ? $search_date_livraison_end : -1, 'search_date_livraison_end_', 0, 0, 1, '', 1, 0, 0, '', '', '', '', 1, '', $langs->trans('to'));
	print '</div>';
	print '</td>';
}
if(!empty($arrayfields['r.objet']['checked'])){
	print '<td class="liste_titre">';
	print '<input class="flat" size="40" maxlength="255" type="text" name="search_objet" value="'.$search_objet.'">';
	print '</td>';
}
if(!empty($arrayfields['r.compte_rendu']['checked'])){
	print '<td class="liste_titre">';
	print '<input class="flat" size="40" maxlength="255" type="text" name="search_compte_rendu" value="'.$search_compte_rendu.'">';
	print '</td>';
}
if(!empty($arrayfields['r.fk_statut']['checked'])){
	print '<td class="liste_titre maxwidthonsmartphone center">';
	$liststatut = array(
		Rendezvous::STATUT_PLANIFIE=>$langs->trans("StatutPlanifie"),
		Rendezvous::STATUT_REALISE=>$langs->trans("StatutRealise"),
		Rendezvous::STATUT_ANNULE=>$langs->trans("StatutAnnule"),
		Rendezvous::STATUT_REPORTE=>$langs->trans("StatutReporte")
	);
	print $form->selectarray('search_statut', $liststatut, $search_statut, -1, 0, 0, '', 0, 0, 0, '', 'maxwidth125', 1);
	print '</td>';
}
if(!empty($arrayfields['r.datec']['checked'])){
	print '<td class="liste_titre center">';
	print '<div class="nowrap">';
	print $form->selectDate($search_datec_start ? $search_datec_start : -1, 'search_datec_start_', 0, 0, 1, '', 1, 0, 0, '', '', '', '', 1, '', $langs->trans('From'));
	print '</div>';
	print '<div class="nowrap">';
	print $form->selectDate($search_datec_end ? $search_datec_end : -1, 'search_datec_end_', 0, 0, 1, '', 1, 0, 0, '', '', '', '', 1, '', $langs->trans('to'));
	print '</div>';
	print '</td>';
}

// Action column
print '<td class="liste_titre" align="middle">';
$searchpicto = $form->showFilterButtons();
print $searchpicto;
print '</td>';

print "</tr>\n";

if(!empty($conf->global->MAIN_AVIMM_STICKY_ENTETELISTE)){
	print '<tr class="liste_titre" id="fourthsticky" style="position: sticky; z-index: 1000;">';
	print '<script>
	$(document).ready(function(){
		var secondsticky = $("#secondsticky").outerHeight(true);
		if(!$.isNumeric(secondsticky)){
			secondsticky = 0;
		}
		var top = $("#id-top").outerHeight(true) + secondsticky + $("#thirdsticky").outerHeight(true);
		$("#fourthsticky").css("top", Math.floor(top)+"px");
	});
	</script>';
}else{
	print '<tr class="liste_titre">';
}

if(!empty($arrayfields['r.rowid']['checked'])){
	print_liste_field_titre($arrayfields['r.rowid']['label'], $_SERVER["PHP_SELF"], 'r.rowid', '', $param, '', $sortfield, $sortorder);
}
if(!empty($arrayfields['s.nom']['checked'])){
	print_liste_field_titre($arrayfields['s.nom']['label'], $_SERVER["PHP_SELF"], 's.nom', '', $param, '', $sortfield, $sortorder);
}
if(!empty($arrayfields['r.numero']['checked'])){
	print_liste_field_titre($arrayfields['r.numero']['label'], $_SERVER["PHP_SELF"], 'r.numero', '', $param, '', $sortfield, $sortorder);
}
if(!empty($arrayfields['r.date']['checked'])){
	print_liste_field_titre($arrayfields['r.date']['label'], $_SERVER["PHP_SELF"], 'r.date', '', $param, '', $sortfield, $sortorder, 'center ');
}
if(!empty($arrayfields['r.date_demo']['checked'])){
	print_liste_field_titre($arrayfields['r.date_demo']['label'], $_SERVER["PHP_SELF"], 'r.date_demo', '', $param, '', $sortfield, $sortorder, 'center ');
}
if(!empty($arrayfields['r.date_livraison']['checked'])){
	print_liste_field_titre($arrayfields['r.date_livraison']['label'], $_SERVER["PHP_SELF"], 'r.date_livraison', '', $param, '', $sortfield, $sortorder, 'center ');
}
if(!empty($arrayfields['r.objet']['checked'])){
	print_liste_field_titre($arrayfields['r.objet']['label'], $_SERVER["PHP_SELF"], 'r.objet', '', $param, '', $sortfield, $sortorder);
}
if(!empty($arrayfields['r.compte_rendu']['checked'])){
	print_liste_field_titre($arrayfields['r.compte_rendu']['label'], $_SERVER["PHP_SELF"], 'r.compte_rendu', '', $param, '', $sortfield, $sortorder);
}
if(!empty($arrayfields['r.fk_statut']['checked'])){
	print_liste_field_titre($arrayfields['r.fk_statut']['label'], $_SERVER["PHP_SELF"], 'r.fk_statut', '', $param, '', $sortfield, $sortorder, 'center ');
}
if(!empty($arrayfields['r.datec']['checked'])){
	print_liste_field_titre($arrayfields['r.datec']['label'], $_SERVER["PHP_SELF"], 'r.datec', '', $param, '', $sortfield, $sortorder, 'center ');
}
print_liste_field_titre($selectedfields, $_SERVER["PHP_SELF"], "", '', $param, '', $sortfield, $sortorder, 'maxwidthsearch center ');

print "</tr>\n";

function getNomUrl($object ,$withpicto = 0, $option = '', $maxlen = 0, $notooltip = 0, $save_lastsearch_value = -1, $noaliasinname = 0, $target = '')
{
	global $conf, $langs, $obj;

	if (!empty($conf->dol_no_mouse_hover)) {
		$notooltip = 1; // Force disable tooltips
	}

	$name = $object->name ? $object->name : $object->nom;

	if (!empty($conf->global->SOCIETE_ON_SEARCH_AND_LIST_GO_ON_CUSTOMER_OR_SUPPLIER_CARD)) {
		if (empty($option) && $object->client > 0) {
			$option = 'customer';
		}
		if (empty($option) && $object->fournisseur > 0) {
			$option = 'supplier';
		}
	}

	if (!empty($conf->global->SOCIETE_ADD_REF_IN_LIST) && (!empty($withpicto))) {
		$code = '';
		if (($object->client) && (!empty($object->code_client)) && ($conf->global->SOCIETE_ADD_REF_IN_LIST == 1 || $conf->global->SOCIETE_ADD_REF_IN_LIST == 2)) {
			$code = $object->code_client.' - ';
		}

		if (($object->fournisseur) && (!empty($object->code_fournisseur)) && ($conf->global->SOCIETE_ADD_REF_IN_LIST == 1 || $conf->global->SOCIETE_ADD_REF_IN_LIST == 3)) {
			$code .= $object->code_fournisseur.' - ';
		}

		if ($code) {
			if ($conf->global->SOCIETE_ADD_REF_IN_LIST == 1) {
				$name = $code.' '.$name;
			} else {
				$name = $code;
			}
		}
	}

	if (!empty($object->name_alias) && empty($noaliasinname)) {
		$name .= ' ('.$object->name_alias.')';
	}

	$result = ''; $label = ''; $label2 = '';
	$linkstart = ''; $linkend = '';

	if (!empty($object->logo) && class_exists('Form')) {
		$label .= '<div class="photointooltip floatright">';
		$label .= Form::showphoto('societe', $object, 0, 40, 0, 'photoref', 'mini', 0); // Important, we must force height so image will have height tags and if image is inside a tooltip, the tooltip manager can calculate height and position correctly the tooltip.
		$label .= '</div>';
		//$label .= '<div style="clear: both;"></div>';
	}

	$label .= '<div class="centpercent">';

	$label .= img_picto('', $object->picto).' <u class="paddingrightonly">'.$langs->trans("ThirdParty").'</u>';
	$linkstart = '<a href="'.DOL_URL_ROOT.'/custom/rendezvousclient/rdv/card.php?id='.$obj->rowid;

	if (isset($object->status)) {
		$label .= ' '.$object->getLibStatut(5);
	}

	$label .= '<br><b>'.$langs->trans('Name').':</b> '.dol_escape_htmltag($object->name);
	if (!empty($object->name_alias)) {
		$label .= ' ('.dol_escape_htmltag($object->name_alias).')';
	}
	if ($object->email) {
		$label .= '<br>'.img_picto('', 'email', 'class="pictofixedwidth"').$object->email;
	}
	if (!empty($object->phone) || !empty($object->fax)) {
		$phonelist = array();
		if ($object->phone) {
			$phonelist[] = dol_print_phone($object->phone, $object->country_code, $object->id, 0, '', '&nbsp', 'phone');
		}
		if ($object->fax) {
			$phonelist[] = dol_print_phone($object->fax, $object->country_code, $object->id, 0, '', '&nbsp', 'fax');
		}
		$label .= '<br>'.implode('&nbsp;', $phonelist);
	}

	if (!empty($object->address)) {
		$label2 .= '<br><b>'.$langs->trans("Address").':</b> '.dol_format_address($object, 1, ' ', $langs); // Address + country
	} elseif (!empty($object->country_code)) {
		$label2 .= '<br><b>'.$langs->trans('Country').':</b> '.$object->country_code;
	}
	if (!empty($object->tva_intra) || (!empty($conf->global->SOCIETE_SHOW_FIELD_IN_TOOLTIP) && strpos($conf->global->SOCIETE_SHOW_FIELD_IN_TOOLTIP, 'vatnumber') !== false)) {
		$label2 .= '<br><b>'.$langs->trans('VATIntra').':</b> '.dol_escape_htmltag($object->tva_intra);
	}

	if (!empty($conf->global->SOCIETE_SHOW_FIELD_IN_TOOLTIP)) {
		if (strpos($conf->global->SOCIETE_SHOW_FIELD_IN_TOOLTIP, 'profid1') !== false && $langs->trans('ProfId1'.$object->country_code) != '-') {
			$label2 .= '<br><b>'.$langs->trans('ProfId1'.$object->country_code).':</b> '.$object->idprof1;
		}
		if (strpos($conf->global->SOCIETE_SHOW_FIELD_IN_TOOLTIP, 'profid2') !== false && $langs->trans('ProfId2'.$object->country_code) != '-') {
			$label2 .= '<br><b>'.$langs->trans('ProfId2'.$object->country_code).':</b> '.$object->idprof2;
		}
		if (strpos($conf->global->SOCIETE_SHOW_FIELD_IN_TOOLTIP, 'profid3') !== false && $langs->trans('ProfId3'.$object->country_code) != '-') {
			$label2 .= '<br><b>'.$langs->trans('ProfId3'.$object->country_code).':</b> '.$object->idprof3;
		}
		if (strpos($conf->global->SOCIETE_SHOW_FIELD_IN_TOOLTIP, 'profid4') !== false && $langs->trans('ProfId4'.$object->country_code) != '-') {
			$label2 .= '<br><b>'.$langs->trans('ProfId4'.$object->country_code).':</b> '.$object->idprof4;
		}
		if (strpos($conf->global->SOCIETE_SHOW_FIELD_IN_TOOLTIP, 'profid5') !== false && $langs->trans('ProfId5'.$object->country_code) != '-') {
			$label2 .= '<br><b>'.$langs->trans('ProfId5'.$object->country_code).':</b> '.$object->idprof5;
		}
		if (strpos($conf->global->SOCIETE_SHOW_FIELD_IN_TOOLTIP, 'profid6') !== false && $langs->trans('ProfId6'.$object->country_code) != '-') {
			$label2 .= '<br><b>'.$langs->trans('ProfId6'.$object->country_code).':</b> '.$object->idprof6;
		}
	}
	if (!empty($object->code_client) && ($object->client == 1 || $object->client == 3)) {
		$label2 .= '<br><b>'.$langs->trans('CustomerCode').':</b> '.$object->code_client;
	}
	if (!empty($object->code_fournisseur) && $object->fournisseur) {
		$label2 .= '<br><b>'.$langs->trans('SupplierCode').':</b> '.$object->code_fournisseur;
	}
	if (!empty($conf->accounting->enabled) && ($object->client == 1 || $object->client == 3)) {
		$label2 .= '<br><b>'.$langs->trans('CustomerAccountancyCode').':</b> '.($object->code_compta ? $object->code_compta : $object->code_compta_client);
	}
	if (!empty($conf->accounting->enabled) && $object->fournisseur) {
		$label2 .= '<br><b>'.$langs->trans('SupplierAccountancyCode').':</b> '.$object->code_compta_fournisseur;
	}
	$label .= ($label2 ? '<br>'.$label2 : '').'</div>';

	// Add type of canvas
	$linkstart .= (!empty($object->canvas) ? '&canvas='.$object->canvas : '');
	// Add param to save lastsearch_values or not
	$add_save_lastsearch_values = ($save_lastsearch_value == 1 ? 1 : 0);
	if ($save_lastsearch_value == -1 && preg_match('/list\.php/', $_SERVER["PHP_SELF"])) {
		$add_save_lastsearch_values = 1;
	}
	if ($add_save_lastsearch_values) {
		$linkstart .= '&save_lastsearch_values=1';
	}
	$linkstart .= '"';

	$linkclose = '';
	if (empty($notooltip)) {
		if (!empty($conf->global->MAIN_OPTIMIZEFORTEXTBROWSER)) {
			$label = $langs->trans("ShowCompany");
			$linkclose .= ' alt="'.dol_escape_htmltag($label, 1).'"';
		}
		$linkclose .= ' title="'.dol_escape_htmltag($label, 1).'"';
		$linkclose .= ' class="classfortooltip refurl"';
		$target_value = array('_self', '_blank', '_parent', '_top');
		if (in_array($target, $target_value)) {
			$linkclose .= ' target="'.dol_escape_htmltag($target).'"';
		}
	}
	$linkstart .= $linkclose.'>';
	$linkend = '</a>';

	global $user;
	if (empty($user->rights->societe->client->voir) && $user->socid > 0 && $object->id != $user->socid) {
		$linkstart = '';
		$linkend = '';
	}

	$result .= $linkstart;
	if ($withpicto) {
		$result .= img_object(($notooltip ? '' : $label), ($object->picto ? $object->picto : 'generic'), ($notooltip ? (($withpicto != 2) ? 'class="paddingright"' : '') : 'class="'.(($withpicto != 2) ? 'paddingright ' : '').'classfortooltip"'), 0, 0, $notooltip ? 0 : 1);
	}
	if ($withpicto != 2) {
		$result .= dol_escape_htmltag($maxlen ? dol_trunc($name, $maxlen) : $name);
	}
	$result .= $linkend;

	return $result;
}

$getNomUrl_cache = array();

$imaxinloop = ($limit ? min($num, $limit) : $num);
$last_num = min($num, $limit);
$i = 0;
while ($i < $imaxinloop) {
	$obj = $db->fetch_object($resql);

	$societe->fetch($obj->fk_soc);

	print "<tr onclick=\"window.location='card.php?id=".$obj->rowid."&save_lastsearch_values=1'\" class=\"oddeven\">";

	// print '<tr class="oddeven">';

	// Id
	if (!empty($arrayfields['r.rowid']['checked'])) {
		print '<td class="nowraponall">';
		print $obj->rowid;
		print '</td>';
	}

	// Nom societe
	if($arrayfields['s.nom']['checked']){
		print '<td class="nowraponall">';
		print getNomUrl($societe, 1, 'custom');
		print '</td>';
	}

	// Numero
	if($arrayfields['r.numero']['checked']){
		print '<td class="nowraponall">';
		print $obj->numero;
		print '</td>';
	}

	// Date rdv
	if($arrayfields['r.date']['checked']){
		print '<td class="nowraponall center">';
		print dol_print_date($db->jdate($obj->date), 'dayhour');
		print '</td>';
	}

	// Date demo
	if($arrayfields['r.date_demo']['checked']){
		print '<td class="nowraponall center">';
		print dol_print_date($db->jdate($obj->date_demo), 'dayhour');
		print '</td>';
	}

	// Date livraison
	if($arrayfields['r.date_livraison']['checked']){
		print '<td class="nowraponall center">';
		print dol_print_date($db->jdate($obj->date_livraison), 'dayhour');
		print '</td>';
	}

	// Objet
	if($arrayfields['r.objet']['checked']){
		print '<td class="nowraponall">';
		print $obj->objet;
		print '</td>';
	}

	// Compte rendu
	if($arrayfields['r.compte_rendu']['checked']){
		print '<td class="nowraponall">';
		print dol_htmlentitiesbr($obj->compte_rendu);
		print '</td>';
	}

	// Statut
	if($arrayfields['r.fk_statut']['checked']){
		print '<td class="nowraponall center">';
		print $object->getLibStatut($obj->fk_statut);
		print '</td>';
	}

	// Date creation
	if($arrayfields['r.datec']['checked']){
		print '<td class="nowraponall center">';
		print dol_print_date($db->jdate($obj->datec), 'dayhour');
		print '</td>';
	}


	print "<script>
	function tdclick(e){
		if (!e) var e = window.event;                // Get the window event
		e.cancelBubble = true;                       // IE Stop propagationif (e.stopPropagation)
		e.stopPropagation();  // Other Broswers
	}
	</script>";
	print '<td onclick="tdclick(event)" class="nowrap center">';

	// print '<td class="nowrap" align="center">';

	// FIN HUGON INFORMATIQUE
	if ($massactionbutton || $massaction) {   // If we are in select mode (massactionbutton defined) or if we have already selected and sent an action ($massaction) defined
		$selected = 0;
		if (in_array($obj->rowid, $arrayofselected)) {
			$selected = 1;
		}
		print '<input id="cb'.$obj->rowid.'" class="flat checkforselect" type="checkbox" name="toselect[]" value="'.$obj->rowid.'"'.($selected ? ' checked="checked"' : '').'>';
	}
	print '</td>';

	print '</tr>'."\n";
	$i++;
}

// If no record found
if ($num == 0) {
	$colspan = 1;
	foreach ($arrayfields as $key => $val) {
		if (!empty($val['checked'])) {
			$colspan++;
		}
	}
	print '<tr><td colspan="'.$colspan.'"><span class="opacitymedium">'.$langs->trans("NoRecordFound").'</span></td></tr>';
}

print '</table>';
print '</div>';

print "</form>\n";


// End of page
llxFooter();
$db->close();
