<?php
/*
 *  \file       rendezvousclient/rdv/class/rendezvousclient.class.php
 *  \ingroup    rendezvousclient
 *  \brief      Fichier de classe des rendez-vous
*/

/**
 *  Class des lignes de
 */
class Rendezvous
{
    /**
	 * @var string ID to identify managed object
	 */
	public $element = 'rendez_vous';

	/**
	 * @var string Name of table without prefix where object is stored
	 */
	public $table_element = 'rendez_vous';

    public $db;

	public $error;

    public $rowid;

    public $socid;

    public $fk_projet;

    public $fk_statut;

    public $type;

    public $numero;

    public $date;

    public $date_demo;

    public $date_livraison;

    public $objet;

    public $objectif_client;

    public $besoin_client;

    public $reponse_besoin_client;

    public $compte_rendu;

    public $datec;

    public $fk_user;

    public $socpeople = array();

    public $lastrdv = array();

    public $otherrdv = array();

    const STATUT_PLANIFIE = 1;
    const STATUT_REALISE = 2;
    const STATUT_ANNULE = 3;
    const STATUT_REPORTE = 4;


    /**
	 *	Constructor
	 *
	 *  @param		DoliDB		$db      Database handler
	 */
	public function __construct($db)
	{
		$this->db = $db;
	}

    public function fetch($id)
    {
        // Check parameters
        if(empty($id)){
            return -1;
        }

        $sql = "SELECT rowid, fk_projet, fk_statut, type, numero, date, date_demo, date_livraison, objet, objectif_client, besoin_client, reponse_besoin_client, compte_rendu, datec, fk_user";
        $sql .= " FROM ".MAIN_DB_PREFIX."rendez_vous";
        $sql .= " WHERE rowid = ".$id;

        $resql = $this->db->query($sql);
        if($resql){
            $obj = $this->db->fetch_object($resql);

            $this->rowid = $obj->rowid;
            $this->fk_projet = $obj->fk_projet;
            $this->fk_statut = $obj->fk_statut;
            $this->type = $obj->type;
            $this->numero = $obj->numero;
            $this->date = $obj->date;
            $this->date_demo = $obj->date_demo;
            $this->date_livraison = $obj->date_livraison;
            $this->objet = $obj->objet;
            $this->objectif_client = $obj->objectif_client;
            $this->besoin_client = $obj->besoin_client;
            $this->reponse_besoin_client = $obj->reponse_besoin_client;
            $this->compte_rendu = $obj->compte_rendu;
            $this->datec = $obj->datec;
            $this->fk_user = $obj->fk_user;

            $this->socpeople = $this->fetchSocpeopleRendezVous($obj->rowid);

            return 1;
        }

        return -1;
    }

    /**
     * Create a new rendez-vous
     *
     * @return int ID of created record if OK, -1 if error
     */
    public function create()
    {
        global $user;

        // Check required fields
        if (empty($this->fk_projet) || empty($this->objet)) {
            $this->error = "Missing required fields";
            return -1;
        }

        $this->datec = dol_now();
        if (empty($this->fk_user)) {
            $this->fk_user = $user->id;
        }

        $sql = "INSERT INTO ".MAIN_DB_PREFIX."rendez_vous (";
        $sql .= "fk_projet, fk_statut, type, numero, date, date_demo, date_livraison, objet, objectif_client, besoin_client, reponse_besoin_client, compte_rendu, datec, fk_user";
        $sql .= ") VALUES (";
        $sql .= $this->fk_projet.", ";
        $sql .= ($this->fk_statut ? $this->fk_statut : "1").", ";
        $sql .= ($this->type ? $this->type : "1").", ";
        $sql .= "'".$this->db->escape($this->numero)."', ";
        $sql .= ($this->date ? "'".$this->db->idate($this->date)."'" : "NULL").", ";
        $sql .= ($this->date_demo ? "'".$this->db->idate($this->date_demo)."'" : "NULL").", ";
        $sql .= ($this->date_livraison ? "'".$this->db->idate($this->date_livraison)."'" : "NULL").", ";
        $sql .= "'".$this->db->escape($this->objet)."', ";
        $sql .= "'".$this->db->escape($this->objectif_client)."', ";
        $sql .= "'".$this->db->escape($this->besoin_client)."', ";
        $sql .= "'".$this->db->escape($this->reponse_besoin_client)."', ";
        $sql .= "'".$this->db->escape($this->compte_rendu)."', ";
        $sql .= "'".$this->db->idate($this->datec)."', ";
        $sql .= $this->fk_user;
        $sql .= ")";

        $resql = $this->db->query($sql);
        if($resql){
            $this->rowid = $this->db->last_insert_id(MAIN_DB_PREFIX."rendez_vous");

            // Save contacts
            if (is_array($this->socpeople) && count($this->socpeople) > 0) {
                $this->saveSocpeopleRendezVous();
            }

            return $this->rowid;
        } else {
            $this->error = $this->db->lasterror();
            return -1;
        }
    }

    /**
     * Update a rendez-vous
     *
     * @return int 1 if OK, -1 if error
     */
    public function update()
    {
        // Check required fields
        if (empty($this->rowid) || empty($this->fk_projet) || empty($this->objet)) {
            $this->error = "Missing required fields";
            return -1;
        }

        $sql = "UPDATE ".MAIN_DB_PREFIX."rendez_vous SET ";
        $sql .= "fk_projet = ".$this->fk_projet.", ";
        $sql .= "fk_statut = ".($this->fk_statut ? $this->fk_statut : "1").", ";
        $sql .= "type = ".($this->type ? $this->type : "1").", ";
        $sql .= "numero = '".$this->db->escape($this->numero)."', ";
        $sql .= "date = ".($this->date ? "'".$this->db->idate($this->date)."'" : "NULL").", ";
        $sql .= "date_demo = ".($this->date_demo ? "'".$this->db->idate($this->date_demo)."'" : "NULL").", ";
        $sql .= "date_livraison = ".($this->date_livraison ? "'".$this->db->idate($this->date_livraison)."'" : "NULL").", ";
        $sql .= "objet = '".$this->db->escape($this->objet)."', ";
        $sql .= "objectif_client = '".$this->db->escape($this->objectif_client)."', ";
        $sql .= "besoin_client = '".$this->db->escape($this->besoin_client)."', ";
        $sql .= "reponse_besoin_client = '".$this->db->escape($this->reponse_besoin_client)."', ";
        $sql .= "compte_rendu = '".$this->db->escape($this->compte_rendu)."', ";
        $sql .= "fk_user = ".$this->fk_user;
        $sql .= " WHERE rowid = ".$this->rowid;

        $resql = $this->db->query($sql);
        if($resql){
            // Update contacts
            $this->deleteSocpeopleRendezVous();
            if (is_array($this->socpeople) && count($this->socpeople) > 0) {
                $this->saveSocpeopleRendezVous();
            }

            return 1;
        } else {
            $this->error = $this->db->lasterror();
            return -1;
        }
    }

    /**
	 *	recupere les contacts d'un rendez vous
	 */
    public function fetchSocpeopleRendezVous($fk_rendez_vous){
        // Check parameters
        if(empty($fk_rendez_vous)){
            return -1;
        }

        $sql = "SELECT fk_socpeople FROM ".MAIN_DB_PREFIX."rendez_vous_socpeople WHERE fk_rendez_vous = ".$fk_rendez_vous;

        $resql = $this->db->query($sql);
        if($resql){
            $num = $this->db->num_rows($resql);

            $socpeople = array();
            $i = 0;
            while($i < $num){
                $obj = $this->db->fetch_object($resql);

                $socpeople[] = $obj->fk_socpeople;

                $i++;
            }
            return $socpeople;
        }

        return array();
    }

    /**
     * Save contacts for a rendez-vous
     *
     * @return int 1 if OK, -1 if error
     */
    public function saveSocpeopleRendezVous()
    {
        if (empty($this->rowid) || !is_array($this->socpeople)) {
            return -1;
        }

        foreach ($this->socpeople as $fk_socpeople) {
            if (!empty($fk_socpeople)) {
                $sql = "INSERT INTO ".MAIN_DB_PREFIX."rendez_vous_socpeople (fk_rendez_vous, fk_socpeople) VALUES (".$this->rowid.", ".$fk_socpeople.")";
                $resql = $this->db->query($sql);
                if (!$resql) {
                    $this->error = $this->db->lasterror();
                    return -1;
                }
            }
        }

        return 1;
    }

    /**
     * Delete all contacts for a rendez-vous
     *
     * @return int 1 if OK, -1 if error
     */
    public function deleteSocpeopleRendezVous()
    {
        if (empty($this->rowid)) {
            return -1;
        }

        $sql = "DELETE FROM ".MAIN_DB_PREFIX."rendez_vous_socpeople WHERE fk_rendez_vous = ".$this->rowid;
        $resql = $this->db->query($sql);

        if (!$resql) {
            $this->error = $this->db->lasterror();
            return -1;
        }

        return 1;
    }

    /**
	 *	recupere le dernier rendez vous d'un projet
	 */
    public function fetchLastRendezVous($fk_projet){
        // Check parameters
        if(empty($fk_projet)){
            return -1;
        }

        $sql = "SELECT rowid, fk_projet, fk_statut, type, numero, date, date_demo, date_livraison, objet, objectif_client, besoin_client, reponse_besoin_client, compte_rendu, datec, fk_user";
        $sql .= " FROM ".MAIN_DB_PREFIX."rendez_vous";
        $sql .= " WHERE fk_projet = ".$fk_projet;
        $sql .= " ORDER BY rowid DESC LIMIT 1";

        $resql = $this->db->query($sql);
        if($resql){
            $obj = $this->db->fetch_object($resql);

            // Vérifier que l'objet existe et initialiser lastrdv
            if($obj){
                $this->lastrdv[0] = new stdClass();
                $this->lastrdv[0]->rowid = $obj->rowid;
                $this->lastrdv[0]->fk_projet = $obj->fk_projet;
                $this->lastrdv[0]->fk_statut = $obj->fk_statut;
                $this->lastrdv[0]->type = $obj->type;
                $this->lastrdv[0]->numero = $obj->numero;
                $this->lastrdv[0]->date = $obj->date;
                $this->lastrdv[0]->date_demo = $obj->date_demo;
                $this->lastrdv[0]->date_livraison = $obj->date_livraison;
                $this->lastrdv[0]->objet = $obj->objet;
                $this->lastrdv[0]->objectif_client = $obj->objectif_client;
                $this->lastrdv[0]->besoin_client = $obj->besoin_client;
                $this->lastrdv[0]->reponse_besoin_client = $obj->reponse_besoin_client;
                $this->lastrdv[0]->compte_rendu = $obj->compte_rendu;
                $this->lastrdv[0]->datec = $obj->datec;
                $this->lastrdv[0]->fk_user = $obj->fk_user;

                $this->lastrdv[0]->socpeople = $this->fetchSocpeopleRendezVous($obj->rowid);

                return 1;
            } else {
                return 0; // Aucun rendez-vous trouvé
            }
        }else{
            return -1;
        }
    }


    /**
	 *	recupere le prochain numero de rendez vous
	 */
    public function getNextNum($fk_projet){
        // Check parameters
        if(empty($fk_projet)){
            return -1;
        }

        $sql = "SELECT count(rowid) as nb";
        $sql .= " FROM ".MAIN_DB_PREFIX."rendez_vous";
        $sql .= " WHERE fk_projet = ".$fk_projet;

        $resql = $this->db->query($sql);
        if($resql){
            $obj = $this->db->fetch_object($resql);

            return ($obj->nb + 1);

        }else{
            return -1;
        }
    }

    /**
     * Get status label
     *
     * @param int $statut Status code
     * @return string Status label
     */
    public function getLibStatut($statut = null)
    {
        global $langs;

        if ($statut === null) {
            $statut = $this->fk_statut;
        }

        switch ($statut) {
            case self::STATUT_PLANIFIE:
                return $langs->trans("StatutPlanifie");
            case self::STATUT_REALISE:
                return $langs->trans("StatutRealise");
            case self::STATUT_ANNULE:
                return $langs->trans("StatutAnnule");
            case self::STATUT_REPORTE:
                return $langs->trans("StatutReporte");
            default:
                return $langs->trans("StatutPlanifie");
        }
    }

    /**
     * Get type label
     *
     * @param int $type Type code
     * @return string Type label
     */
    public function getLibType($type = null)
    {
        global $langs;

        if ($type === null) {
            $type = $this->type;
        }

        switch ($type) {
            case 1:
                return $langs->trans("ChezClient");
            case 2:
                return $langs->trans("Appel");
            case 3:
                return $langs->trans("Visio");
            default:
                return $langs->trans("ChezClient");
        }
    }

    /**
     * Get URL for this rendez-vous
     *
     * @param int $withpicto Include picto
     * @param string $option Option (not used currently)
     * @return string URL
     */
    public function getNomUrl($withpicto = 0, $option = '')
    {
        $result = '';

        if ($withpicto) {
            $result .= img_picto('', 'action') . ' ';
        }

        $url = DOL_URL_ROOT . '/custom/rendezvousclient/rdv/card.php?id=' . $this->rowid;
        $result .= '<a href="' . $url . '">' . $this->numero . '</a>';

        return $result;
    }

    /**
	 *	recupere le nombre de rendezvous d'un projet
	 */
    public function getNbRendezVous(){
        // Check parameters
        if(empty($this->fk_projet)){
            return -1;
        }

        $sql = "SELECT count(rowid) as nb";
        $sql .= " FROM ".MAIN_DB_PREFIX."rendez_vous";
        $sql .= " WHERE fk_projet = ".$this->fk_projet;

        $resql = $this->db->query($sql);
        if($resql){
            $obj = $this->db->fetch_object($resql);

            if($obj){
                return $obj->nb;
            } else {
                return 0;
            }
        }else{
            return -1;
        }
    }

    /**
	 *	recupere le nombre de rendezvous d'un projet
	 */
    public function getOtherRDV(){
        // Check parameters
        if(empty($this->fk_projet) || empty($this->rowid)){
            return -1;
        }
        $sql = "SELECT rowid, fk_projet, fk_statut, type, numero, date, date_demo, date_livraison, objet, objectif_client, besoin_client, reponse_besoin_client, compte_rendu, datec, fk_user";
        $sql .= " FROM ".MAIN_DB_PREFIX."rendez_vous";
        $sql .= " WHERE fk_projet = ".$this->fk_projet." AND rowid != ".$this->rowid;
        $sql .= " ORDER BY rowid";

        $resql = $this->db->query($sql);
        if($resql){
            $num = $this->db->num_rows($resql);

            $i = 0;

            while($i < $num){
                $obj = $this->db->fetch_object($resql);

                if($obj){
                    $this->otherrdv[$i] = new stdClass();
                    $this->otherrdv[$i]->rowid = $obj->rowid;
                    $this->otherrdv[$i]->fk_projet = $obj->fk_projet;
                    $this->otherrdv[$i]->fk_statut = $obj->fk_statut;
                    $this->otherrdv[$i]->type = $obj->type;
                    $this->otherrdv[$i]->numero = $obj->numero;
                    $this->otherrdv[$i]->date = $obj->date;
                    $this->otherrdv[$i]->date_demo = $obj->date_demo;
                    $this->otherrdv[$i]->date_livraison = $obj->date_livraison;
                    $this->otherrdv[$i]->objet = $obj->objet;
                    $this->otherrdv[$i]->objectif_client = $obj->objectif_client;
                    $this->otherrdv[$i]->besoin_client = $obj->besoin_client;
                    $this->otherrdv[$i]->reponse_besoin_client = $obj->reponse_besoin_client;
                    $this->otherrdv[$i]->compte_rendu = $obj->compte_rendu;
                    $this->otherrdv[$i]->datec = $obj->datec;
                    $this->otherrdv[$i]->fk_user = $obj->fk_user;
                }

                $i++;
            }

            return 1;
        } else {
            return -1;
        }
    }





}
