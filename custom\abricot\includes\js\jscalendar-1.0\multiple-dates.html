<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>Sample for the multiple dates feature</title>
  <!-- calendar stylesheet -->
  <link rel="stylesheet" type="text/css" media="all" href="calendar-win2k-cold-1.css" title="win2k-cold-1" />

  <!-- main calendar program -->
  <script type="text/javascript" src="calendar.js"></script>

  <!-- language for the calendar -->
  <script type="text/javascript" src="lang/calendar-en.js"></script>

  <!-- the following script defines the Calendar.setup helper function, which makes
       adding a calendar a matter of 1 or 2 lines of code. -->
  <script type="text/javascript" src="calendar-setup.js"></script>
</head>

<body>
  <h1>Sample for the multiple dates feature</h1>

  <p>
    Starting version 0.9.7,
    the calendar is able to handle multiple dates selection, in either
    flat or popup form.  For this to happen one needs to pass the
    "<tt>multiple:&nbsp;true</tt>" parameter to
    <tt>Calendar.setup</tt> and to install an <tt>onUpdate</tt>
    handler that watches for modifications.
  </p>

  <a id="trigger" href="#">[open calendar...]</a>

  <div id="output"></div>

  <script type="text/javascript">//<![CDATA[
    // the default multiple dates selected, first time the calendar is instantiated
    var MA = [];

    function closed(cal) {

      // here we'll write the output; this is only for example.  You
      // will normally fill an input field or something with the dates.
      var el = document.getElementById("output");

      // reset initial content.
      el.innerHTML = "";

      // Reset the "MA", in case one triggers the calendar again.
      // CAREFUL!  You don't want to do "MA = [];".  We need to modify
      // the value of the current array, instead of creating a new one.
      // Calendar.setup is called only once! :-)  So be careful.
      MA.length = 0;

      // walk the calendar's multiple dates selection hash
      for (var i in cal.multiple) {
        var d = cal.multiple[i];
        // sometimes the date is not actually selected, that's why we need to check.
        if (d) {
          // OK, selected.  Fill an input field.  Or something.  Just for example,
          // we will display all selected dates in the element having the id "output".
          el.innerHTML += d.print("%A, %Y %B %d") + "<br />";

          // and push it in the "MA", in case one triggers the calendar again.
          MA[MA.length] = d;
        }
      }
      cal.hide();
      return true;
    };

    Calendar.setup({
      align      : "BR",
      showOthers : true,
      multiple   : MA, // pass the initial or computed array of multiple dates to be initially selected
      onClose    : closed,
      button     : "trigger"
    });
  //]]></script>

<hr />
<address><a href="http://dynarch.com/mishoo/">mishoo</a></address>
<!-- hhmts start --> Last modified: Thu Mar  3 20:17:42 EET 2005 <!-- hhmts end -->
</body> </html>
