<?php
/**
 *	\file       rendezvousclient/demo/list.php
 *	\ingroup    rendezvousclient
 *	\brief      Liste des démos créées
 */
require_once '../../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/societe/class/societe.class.php';
require_once DOL_DOCUMENT_ROOT.'/projet/class/project.class.php';
dol_include_once('/rendezvousclient/lib/rendezvousclient.lib.php');
require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.formother.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/date.lib.php';

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'users', 'companies'));

$action = GETPOST('action', 'az09');
$id = GETPOST('id', 'int');
$help_url = '';

/*
 * Actions
*/

if($action == 'delete' && $id > 0){
    // Supprimer une démo
    $sql = "DELETE FROM ".MAIN_DB_PREFIX."rendez_vous_demo WHERE rowid = ".$id;
    $resql = $db->query($sql);

    if($resql){
        setEventMessages($langs->trans("DemoDeleted"), null, 'mesgs');
    } else {
        setEventMessages($langs->trans("ErrorDemoNotDeleted"), null, 'errors');
    }

    header("Location: ".$_SERVER['PHP_SELF']);
    exit;
}

/*
 * View
*/

$form = new Form($db);
$formother = new FormOther($db);

$title = $langs->trans('DemosList');

llxHeader('', $title, $help_url);

print load_fiche_titre($langs->trans("DemosList"), '', 'generic');

// Récupérer la liste des démos
$sql = "SELECT d.rowid, d.fk_projet, d.demo_name, d.demo_url, d.date_creation, d.date_expiration, d.statut, d.notes,";
$sql .= " p.title as projet_title, s.nom as societe_nom";
$sql .= " FROM ".MAIN_DB_PREFIX."rendez_vous_demo d";
$sql .= " LEFT JOIN ".MAIN_DB_PREFIX."projet p ON d.fk_projet = p.rowid";
$sql .= " LEFT JOIN ".MAIN_DB_PREFIX."societe s ON p.fk_soc = s.rowid";
$sql .= " ORDER BY d.date_creation DESC";

$resql = $db->query($sql);

if($resql){
    $num = $db->num_rows($resql);

    print '<div class="div-table-responsive">';
    print '<table class="tagtable liste listwithfilterbefore" width="100%">';

    // En-têtes
    print '<tr class="liste_titre">';
    print '<th>'.$langs->trans("DemoName").'</th>';
    print '<th>'.$langs->trans("Project").'</th>';
    print '<th>'.$langs->trans("ThirdParty").'</th>';
    print '<th>'.$langs->trans("DemoURL").'</th>';
    print '<th>'.$langs->trans("DateCreation").'</th>';
    print '<th>'.$langs->trans("DateExpiration").'</th>';
    print '<th>'.$langs->trans("Status").'</th>';
    print '<th>'.$langs->trans("Actions").'</th>';
    print '</tr>';

    if($num > 0){
        $i = 0;
        while($obj = $db->fetch_object($resql)){
            $i++;

            // Couleur de ligne alternée
            $bc = ($i % 2) ? 'pair' : 'impair';

            print '<tr class="oddeven">';

            // Nom de la démo
            print '<td>'.$obj->demo_name.'</td>';

            // Projet
            $projectstatic = new Project($db);
            if($obj->fk_projet > 0){
                $projectstatic->id = $obj->fk_projet;
                $projectstatic->title = $obj->projet_title;
                print '<td>'.$projectstatic->getNomUrl(1).'</td>';
            } else {
                print '<td>-</td>';
            }

            // Tiers
            print '<td>'.$obj->societe_nom.'</td>';

            // URL de la démo
            if($obj->demo_url){
                print '<td><a href="'.$obj->demo_url.'" target="_blank">'.$obj->demo_url.'</a></td>';
            } else {
                print '<td>-</td>';
            }

            // Date de création
            print '<td>'.dol_print_date($db->jdate($obj->date_creation), 'dayhour').'</td>';

            // Date d'expiration
            if($obj->date_expiration){
                print '<td>'.dol_print_date($db->jdate($obj->date_expiration), 'day').'</td>';
            } else {
                print '<td>-</td>';
            }

            // Statut
            if($obj->statut == 1){
                print '<td><span class="badge badge-status4 badge-status">'.$langs->trans("Active").'</span></td>';
            } else {
                print '<td><span class="badge badge-status8 badge-status">'.$langs->trans("Inactive").'</span></td>';
            }

            // Actions
            print '<td>';
            if($obj->demo_url){
                print '<a href="'.$obj->demo_url.'" target="_blank" title="'.$langs->trans("OpenDemo").'">'.img_picto('', 'globe').'</a> ';
            }
            print '<a href="'.$_SERVER["PHP_SELF"].'?action=delete&id='.$obj->rowid.'&token='.newToken().'" onclick="return confirm(\''.$langs->trans("ConfirmDeleteDemo").'\')" title="'.$langs->trans("Delete").'">'.img_picto('', 'delete').'</a>';
            print '</td>';

            print '</tr>';
        }
    } else {
        print '<tr><td colspan="8" class="opacitymedium">'.$langs->trans("NoDemo").'</td></tr>';
    }

    print '</table>';
    print '</div>';

} else {
    dol_print_error($db);
}

// End of page
llxFooter();
$db->close();
