﻿/*
Copyright (c) 2003-2013, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'de', {
	alertUrl: 'Bitte geben Sie die Bild-URL an',
	alt: 'Alternativer Text',
	border: 'Rahmen',
	btnUpload: 'Zum Server senden',
	button2Img: 'Möchten Sie den gewählten Bild-Button in ein einfaches Bild umwandeln?',
	hSpace: 'Horizontal-Abstand',
	img2Button: 'Möchten Sie das gewählten Bild in einen Bild-Button umwandeln?',
	infoTab: 'Bild-Info',
	linkTab: 'Link',
	lockRatio: 'Größenverhältnis beibehalten',
	menu: 'Bild-Eigenschaften',
	resetSize: 'Gr<PERSON>ße zurücksetzen',
	title: 'Bild-Eigenschaften',
	titleButton: 'Bildbutton-Eigenschaften',
	upload: 'Ho<PERSON>laden',
	urlMissing: 'Imagequelle URL fehlt.',
	vSpace: 'Vertikal-Abstand',
	validateBorder: 'Rahm<PERSON> muß eine ganze Zahl sein.',
	validateHSpace: 'Horizontal-Abstand muß eine ganze Zahl sein.',
	validateVSpace: 'Vertikal-Abstand muß eine ganze Zahl sein.'
});
