<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="ro" lang="ro">

<head>
<title>Bug</title>
<link rel="stylesheet" type="text/css" media="all" href="calendar-win2k-1.css" title="win2k-1" />

<!-- import the calendar script -->
<script type="text/javascript" src="calendar.js"></script>

<!-- import the language module -->
<script type="text/javascript" src="lang/calendar-en.js"></script>

<!-- helper script that uses the calendar -->
<script type="text/javascript">
// This function gets called when the end-user clicks on some date.
function selected(cal, date) {
  cal.sel.value = date; // just update the date in the input field.
  if (cal.sel.id == "sel1" || cal.sel.id == "sel3")
    // if we add this call we close the calendar on single-click.
    // just to exemplify both cases, we are using this only for the 1st
    // and the 3rd field, while 2nd and 4th will still require double-click.
    cal.callCloseHandler();
}

// And this gets called when the end-user clicks on the _selected_ date,
// or clicks on the "Close" button.  It just hides the calendar without
// destroying it.
function closeHandler(cal) {
  cal.hide();                        // hide the calendar
}

// This function shows the calendar under the element having the given id.
// It takes care of catching "mousedown" signals on document and hiding the
// calendar if the click was outside.
function showCalendar(id, format) {
  var el = document.getElementById(id);
  if (calendar != null) {
    // we already have some calendar created
    calendar.hide();                 // so we hide it first.
  } else {
    // first-time call, create the calendar.
    var cal = new Calendar(false, null, selected, closeHandler);
    // uncomment the following line to hide the week numbers
    // cal.weekNumbers = false;
    calendar = cal;                  // remember it in the global var
    cal.setRange(1900, 2070);        // min/max year allowed.
    cal.create();
  }
  calendar.setDateFormat(format);    // set the specified date format
  calendar.parseDate(el.value);      // try to parse the text in field
  calendar.sel = el;                 // inform it what input field we use
  calendar.showAtElement(el);        // show the calendar below it

  return false;
}

var MINUTE = 60 * 1000;
var HOUR = 60 * MINUTE;
var DAY = 24 * HOUR;
var WEEK = 7 * DAY;

// If this handler returns true then the "date" given as
// parameter will be disabled.  In this example we enable
// only days within a range of 10 days from the current
// date.
// You can use the functions date.getFullYear() -- returns the year
// as 4 digit number, date.getMonth() -- returns the month as 0..11,
// and date.getDate() -- returns the date of the month as 1..31, to
// make heavy calculations here.  However, beware that this function
// should be very fast, as it is called for each day in a month when
// the calendar is (re)constructed.
function isDisabled(date) {
  var today = new Date();
  return (Math.abs(date.getTime() - today.getTime()) / DAY) > 10;
}
</script>
</head>

<body>
<form>
<b>Date:</b>
<br>
<input type="text" name="date1" id="sel1" size="30">
<input type="button" value="..." onclick="return showCalendar('sel1', 'y-m-d');">
<p>
<br>
<br><b>Visible &lt;select&gt;, hides and unhides as expected</b>
<br>
<select name="foo" multiple>
<option value="1">can use the functions date.getFullYear() -- returns</option>
<option value="2">4 digit number, date.getMonth() -- returns the month</option>
<option value="3">heavy calculations here.  However, beware that this</option>
</select>

<p>
<br><b>Hidden &lt;select&gt;, it should stay hidden (but doesn't)</b>
<br>
<select name="foo2" multiple style="visibility: hidden">
<option value="1">this should</option>
<option value="2">remain hidden right?</option>
</select>

<p>
<br><b>Hidden textbox below, it stays hidden as expected</b>
<br>
<input type="text" name="foo3" value="this stays hidden just fine" style="visibility: hidden">
</form>
</body></html>
