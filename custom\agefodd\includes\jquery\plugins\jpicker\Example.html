<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <title>Digital Magic Productions - jPicker - A jQuery Color Picker Plugin</title>
  <link rel="Stylesheet" type="text/css" href="css/jpicker-1.1.6.min.css" />
  <link rel="Stylesheet" type="text/css" href="jPicker.css" />
  <script src="jquery-1.4.4.min.js" type="text/javascript"></script>
  <script src="jpicker-1.1.6.min.js" type="text/javascript"></script>
  <script type="text/javascript">
    $(function()
      {
        $.fn.jPicker.defaults.images.clientPath='images/';
        var LiveCallbackElement = $('#Live'),
            LiveCallbackButton = $('#LiveButton');
        $('#Inline').jPicker({window:{title:'Inline Example'}});
        $('#Expandable').jPicker({window:{expandable:true,title:'Expandable Example'}});
        $('#Alpha').jPicker({window:{expandable:true,title:'Alpha (Transparency) Example)',alphaSupport:true},color:{active:new $.jPicker.Color({ahex:'99330099'})}});
        $('#Binded').jPicker({window:{title:'Binded Example'},color:{active:new $.jPicker.Color({ahex:'993300ff'})}});
        $('.Multiple').jPicker({window:{title:'Multiple Binded Example'}});
        $('#Callbacks').jPicker(
          {window:{title:'Callback Example'}},
          function(color, context)
          {
            var all = color.val('all');
            alert('Color chosen - hex: ' + (all && '#' + all.hex || 'none') + ' - alpha: ' + (all && all.a + '%' || 'none'));
            $('#Commit').css({ backgroundColor: all && '#' + all.hex || 'transparent' });
          },
          function(color, context)
          {
            if (context == LiveCallbackButton.get(0)) alert('Color set from button');
            var hex = color.val('hex');
            LiveCallbackElement.css({ backgroundColor: hex && '#' + hex || 'transparent' });
          },
          function(color, context)
          {
            alert('"Cancel" Button Clicked');
          });
        $('#LiveButton').click(
          function()
          {
            $.jPicker.List[7].color.active.val('hex', 'e2ddcf', this);
          });
        $('#AlterColors').jPicker({window:{title:'Color Interaction Example'}});
        $('#GetActiveColor').click(
          function()
          {
            alert($.jPicker.List[8].color.active.val('ahex'));
          });
        $('#GetRG').click(
          function()
          {
            var rg=$.jPicker.List[8].color.active.val('rg');
            alert('red: ' + rg.r + ', green: ' + rg.g);
          });
        $('#SetHue').click(
          function()
          {
            $.jPicker.List[8].color.active.val('h', 133);
          });
        $('#SetValue').click(
          function()
          {
            $.jPicker.List[8].color.active.val('v', 38);
          });
        $('#SetRG').click(
          function()
          {
            $.jPicker.List[8].color.active.val('rg', { r: 213, g: 118 });
          });
      });
  </script>
</head>
<body>
  <div id="jPicker">
    <h2>jPicker - A jQuery Color Picker Plugin.</h2>
    <p>
      jPicker is a fast, lightweight jQuery plugin for including an advanced color picker in your web projects. It has been painstakenly ported from John Dyers' awesome work on his picker using the Prototype framework.<br /><br />
      jPicker supports all current browsers and has been extensively tested in Chrome, Firefox, IE5.5+, Safari, and Opera.<br /><br />
      If you are updating a current version, you MUST always use the CSS and image files from the download as there may have been changes.<br /><br />
      If you are moving from a V1.0.* version, you MUST read the docs below to implement some changes to the Color object returned by the callback functions.<br /><br />
      <a href="http://johndyer.name/post/2007/09/PhotoShop-like-JavaScript-Color-Picker.aspx" class="newWindow">View John Dyer's prototype plugin here.</a><br /><br />
      View jPicker details a docs below.<br /><br />
      <a href="http://code.google.com/p/jpicker/" class="newWindow">Check out the source from Google Code.</a>
    </p><hr /><br />
    <h2>jPicker Inline Example</h2>
    <p>
      jPicker can be used inline by binding to any block level element.<br /><br />
      <code>jPicker() -- no arguments</code>
      <pre>
&lt;script type="text/javascript"&gt;        
  $(document).ready(
    function()
    {
      $('#Inline').jPicker();
    });
&lt;/script&gt;
&lt;div id="Inline"&gt;&lt;/div&gt;</pre>
      <div id="Inline"></div>
    </p><hr /><br />
    <h2>jPicker Expandable Example</h2>
    <p>
      jPicker can also display only a small picker icon that opens a popup for editing.<br /><br />
      <code>jPicker({ window: { expandable: true }})</code>
      <pre>
&lt;script type="text/javascript"&gt;
  $(document).ready(
    function()
    {
      $('#Expandable').jPicker(
        {
          window:
          {
            expandable: true
          }
        });
    });
&lt;/script&gt;
&lt;span id="Expandable"&gt;&lt;/span&gt;</pre>
      <span id="Expandable"></span>
    </p><hr /><br />
    <h2>jPicker Alpha Channel Example</h2>
    <p>
      jPicker can also pick colors with alpha (transparency) values.<br /><br />
      <code>jPicker({ window: { expandable: true }})</code>
      <pre>
&lt;script type="text/javascript"&gt;        
  $(document).ready(
    function()
    {
      $('#Alpha').jPicker(
        {
          window:
          {
            expandable: true
          },
          color:
          {
            alphaSupport: true,
            active: new $.jPicker.Color({ ahex: '99330099' })
          }
        });
    });
&lt;/script&gt;
&lt;span id="Alpha"&gt;&lt;/span&gt;</pre>
      <span id="Alpha"></span>
    </p><hr /><br />
    <h2>jPicker Binded Example</h2>
    <p>
      jPicker can also be binded to an input element.<br /><br />
      <code>jPicker() -- no arguments</code>
      <pre>
&lt;script type="text/javascript"&gt;        
  $(document).ready(
    function()
    {
      $('#Binded').jPicker();
    });
&lt;/script&gt;
&lt;input id="Binded" type="text" value="e2ddcf" /&gt;</pre>
      <input id="Binded" type="text" value="e2ddcf" />
    </p><hr /><br />
    <h2>Multiple jPicker Binded Example</h2>
    <p>
      jPicker can also be binded to multiple elements at a time.<br /><br />
      <code>jPicker() -- no arguments</code>
      <pre>
&lt;script type="text/javascript"&gt;        
  $(document).ready(
    function()
    {
      $('.Multiple').jPicker();
    });
&lt;/script&gt;
&lt;input class="Multiple" type="text" value="e2ddcf" /&gt;&lt;br /&gt;
&lt;input class="Multiple" type="text" value="" /&gt;&lt;br /&gt;
&lt;input class="Multiple" type="text" value="fda0f7" /&gt;</pre>
      <input class="Multiple" type="text" value="e2ddcf" /><br />
      <input class="Multiple" type="text" value="" /><br />
      <input class="Multiple" type="text" value="fda0f7" /><br />
    </p><hr /><br />
    <h2>jPicker Callback Functions</h2>
    <p>
      Register for callback function to have it interact with your page.<br /><br />
      <code>jPicker([settings, [commitCallback, [liveCallback, [cancelCallback]]]])</code>
      <pre>
&lt;script type="text/javascript"&gt;
  $(document).ready(
    function()
    {
      var LiveCallbackElement = $('#Live'),
          LiveCallbackButton = $('#LiveButton');  // you don't want it searching this
                                                  // on every live callback!!!
      $('#Callbacks').jPicker(
        {},
        function(color, context)
        {
          var all = color.val('all');
          alert('Color chosen - hex: ' + (all && '#' + all.hex || 'none') +
            ' - alpha: ' + (all && all.a + '%' || 'none'));
          $('#Commit').css(
            {
              backgroundColor: all && '#' + all.hex || 'transparent'
            }); // prevent IE from throwing exception if hex is empty
        },
        function(color, context)
        {
          if (context == LiveCallbackButton.get(0)) alert('Color set from button');
          var hex = color.val('hex');
          LiveCallbackElement.css(
            {
              backgroundColor: hex && '#' + hex || 'transparent'
            }); // prevent IE from throwing exception if hex is empty
        },
        function(color, context)
        {
          alert('"Cancel" Button Clicked');
        });      
      $('#LiveButton').click(
        function()
        {
          $.jPicker.List[0].color.active.val('hex', 'e2ddcf', this);
        });
    });
&lt;/script&gt;
&lt;input id="Callbacks" type="text" value="e2ddcf" /&gt;
&lt;span id="Commit" style="background-color: #e2ddcf; display: block; --
          float: left; height: 50px; margin: 10px; width: 50px;"&gt; --
          Commit&lt;/span&gt;
&lt;span id="Live" style="display: block; float: left; height: 50px; --
          margin: 10px; width: 50px;"&gt;Live&lt;/span&gt;
&lt;input id="LiveButton" type="button" value="Set To #e2ddcf" /&gt;</pre>
      <input id="Callbacks" type="text" value="e2ddcf" /><br />
      <span id="Commit" style="background-color: #e2ddcf; display: block; float: left; height: 50px; margin: 10px; width: 50px;">Commit</span>
      <span id="Live" style="display: block; float: left; height: 50px; margin: 10px; width: 50px;">Live</span>
      <input id="LiveButton" type="button" value="Set To #e2ddcf" />
    </p><hr /><br />
    <h2>jPicker Settings And Colors</h2>
    <p>
      Use the "val" method on the active color for interaction with the picker.<br /><br />
      <code>(jQuery).jPicker.List[index]</code>
      <pre>
&lt;script type="text/javascript"&gt;        
  $(document).ready(
    function()
    {
      $('#AlterColors').jPicker();
      $('#GetActiveColor').click(
        function()
        {
          alert($.jPicker.List[0].color.active.val('ahex'));
        });
      $('#GetRG').click(
        function()
        {
          var rg=$.jPicker.List[0].color.active.val('rg');
          alert('red: ' + rg.r + ', green: ' + rg.g);
        });
      $('#SetHue').click(
        function()
        {
          $.jPicker.List[0].color.active.val('h', 133);
        });
      $('#SetValue').click(
        function()
        {
          $.jPicker.List[0].color.active.val('v', 38);
        });
      $('#SetRG').click(
        function()
        {
          $.jPicker.List[0].color.active.val('rg', { r: 213, g: 118 });
        });
    });
&lt;/script&gt;
&lt;input id="AlterColors" type="text" value="e2ddcf" /&gt;&lt;br /&gt;
&lt;input id="GetActiveColor" type="button" value="Get Active Color" /&gt;&lt;br /&gt;
&lt;input id="GetRG" type="button" value="Get Red/Green Value" /&gt;&lt;br /&gt;
&lt;input id="SetHue" type="button" value="Set Hue To 133" /&gt;&lt;br /&gt;
&lt;input id="SetValue" type="button" value="Set Value To 38" /&gt;&lt;br /&gt;
&lt;input id="SetRG" type="button" value="Set Red/Green To 213, 118" /&gt;</pre>
      <input id="AlterColors" type="text" value="e2ddcf" /><br />
      <input id="GetActiveColor" type="button" value="Get Active Color" /><br />
      <input id="GetRG" type="button" value="Get Red/Green Value" /><br />
      <input id="SetHue" type="button" value="Set Hue To 133" /><br />
      <input id="SetValue" type="button" value="Set Value To 38" /><br />
      <input id="SetRG" type="button" value="Set Red/Green To 213, 118" />
    </p><hr /><br />
    <h2>jPicker Core</h2>
    <p>
      jPicker Core function - returns the jQuery object.<br /><br />
      <code>jPicker([settings, [commitCallback, [liveCallback, [cancelCallback]]]])</code>
    </p><hr /><br />
    <h2>Settings</h2>
    <p>
      settings [object]: (with defaults)<br />
      <pre>
{
  window: // used to define the position of the popup window
          // only useful in binded mode
  {
    title: null, // any title for the jPicker window itself - displays
                 // "Drag Markers To Pick A Color" if left null
    position:
    {
      x: 'screenCenter', // acceptable values "left", "center", "right",
                         // "screenCenter", or relative px value
      y: 'top', // acceptable values "top", "bottom", "center", or relative px
                // value
    },
    expandable: false, // default to large static picker - set to true to make an
                       // expandable picker (small icon with popup) - set
                       // automatically when binded to input element
    liveUpdate: true, // set false if you want the user to click "OK" before the
                      // binded input box updates values (always "true" for
                      // expandable picker)
    alphaSupport: false, // set to true to enable alpha picking
    alphaPrecision: 0, // set decimal precision for alpha percentage display -
                       // hex codes do not map directly to percentage integers -
                       // range 0-2
    updateInputColor: true // set to false to prevent binded input colors from
                           // changing
  },
  color:
  {
    mode: 'h', // acceptable values "h" (hue), "s" (saturation), "v" (brightness),
               // "r" (red), "g" (green), "b" (blue), "a" (alpha)
    active: new $.jPicker.Color({ hex: 'ffc000' }), // accepts any declared
               // jPicker.Color object or hex string WITH OR WITHOUT '#'
    quickList: // this list of quick pick colors - override for a different list
      [
        new $.jPicker.Color({ h: 360, s: 33, v: 100}), // accepts any declared
               // jPicker.Color object or hex string WITH OR WITHOUT '#'
        new $.jPicker.Color({ h: 360, s: 66, v: 100}),
        (...) // removed for brevity
        new $.jPicker.Color({ h: 330, s: 100, v: 50}),
        new $.jPicker.Color()
      ]
  },
  images
  {
    clientPath: '/jPicker/images/', // Path to image files
    colorMap: // colorMap size and arrow icon
    {
      width: 256, // Map width - don't override unless using a smaller image set
      height: 256, // Map height - don't override unles using a smaller image set
      arrow:
      {
        file: 'mappoint.gif', // Arrow icon image file
        width: 15, // Arrow icon width
        height: 15 // Arrow icon height
      }
    },
    colorBar: // colorBar size and arrow icon
    {
      width: 20, // Bar width - don't override unless using a smaller image set
      height: 256, // Bar height - don't override unless using a smaller image set
      arrow:
      {
        file: 'rangearrows.gif', // Arrow icon image file
        width: 40, // Arrow icon width
        height: 9 // Arrow icon height
      }
    },
    picker: // picker icon and size
    {
      file: 'picker.gif', // Picker icon image file
      width: 25, // Picker width - don't override unless using a smaller image set
      height: 24 // Picker height - don't override unless using a smaller image set
    }
  },
  localization:
  {
    text:
    {
      title: 'Drag Markers To Pick A Color',
      newColor: 'new',
      currentColor: 'current',
      ok: 'OK',
      cancel: 'Cancel'
    },
    tooltips:
    {
      colors:
      {
        newColor: 'New Color - Press &ldquo;OK&rdquo; To Commit',
        currentColor: 'Click To Revert To Original Color'
      },
      buttons:
      {
        ok: 'Commit To This Color Selection',
        cancel: 'Cancel And Revert To Original Color'
      },
      hue:
      {
        radio: 'Set To &ldquo;Hue&rdquo; Color Mode',
        textbox: 'Enter A &ldquo;Hue&rdquo; Value (0-360&deg;)'
      },
      saturation:
      {
        radio: 'Set To &ldquo;Saturation&rdquo; Color Mode',
        textbox: 'Enter A &ldquo;Saturation&rdquo; Value (0-100%)'
      },
      brightness:
      {
        radio: 'Set To &ldquo;Brightness&rdquo; Color Mode',
        textbox: 'Enter A &ldquo;Brightness&rdquo; Value (0-100%)'
      },
      red:
      {
        radio: 'Set To &ldquo;Red&rdquo; Color Mode',
        textbox: 'Enter A &ldquo;Red&rdquo; Value (0-255)'
      },
      green:
      {
        radio: 'Set To &ldquo;Green&rdquo; Color Mode',
        textbox: 'Enter A &ldquo;Green&rdquo; Value (0-255)'
      },
      blue:
      {
        radio: 'Set To &ldquo;Blue&rdquo; Color Mode',
        textbox: 'Enter A &ldquo;Blue&rdquo; Value (0-255)'
      },
      alpha:
      {
        radio: 'Set To &ldquo;Alpha&rdquo; Color Mode',
        textbox: 'Enter A &ldquo;Alpha&rdquo; Value (0-100)'
      },
      hex:
      {
        textbox: 'Enter A &ldquo;Hex&rdquo; Color Value (#000000-#ffffff)',
        alpha: 'Enter A &ldquo;Alpha&rdquo; Value (#00-#ff)'
      }
    }
  }
}</pre>
    </p><hr /><br />
    <h2>Callback Pattern</h2>
    <p>
      <code>function(jPicker.Color color, object context){...}</code>
    </p><hr /><br />
    <h2>jPicker List</h2>
    <p>
      The list of active jPicker objects.<br /><br />
      <code>(jQuery).jPicker.List[]</code>
    </p><hr /><br />
    <h2>jPicker Color Class</h2>
    <p>
      Definition of the jPicker.Color class.<br />
      <pre>
(jQuery).jPicker.Color()
(jQuery).jPicker.Color({ ahex: 'ffffffff' })
(jQuery).jPicker.Color({ hex: 'ffffff', [a: (0-255)] })
(jQuery).jPicker.Color({ r: (0-255), g: (0-255), b: (0-255), [a: (0-255)] })
(jQuery).jPicker.Color({ h: (0-360), s: (0-100), v: (0-100), [a: (0-255)] })
{
  val: function(name, value, context),
  bind: function(callback) where callback is function(color, [context]),
  unbind: function(callback)
}

method "val" usage

val(name) : get value

  'r':     red         (0-255)
  'g':     green       (0-255)
  'b':     blue        (0-255)
  'a':     alpha       (0-255)
  'h':     hue         (0-360)
  's':     saturation  (0-100)
  'v':     value       (0-100)
  'hex':   hex         (000000-ffffff)
  'ahex':  ahex        (00000000-ffffffff)
  'all':   all         all
  
  ex. Usage

    val('r'):     (0-255)
    val('h'):     (0-360)
    val('hex'):   (000000-ffffff)
    val('rg'):    { r: (0-255), g: (0-255) }
    val('rgba'):  { r: (0-255), g: (0-255), b: (0-255), a: (0-255) }
    val('hvga'):  { h: (0-255), v: (0-100), g: (0-255), a: (0-255) }
    val('all'):   { r: (0-255), g: (0-255), b: (0-255), a: (0-255), h: (0-360) --
                    s: (0-100), v: (0-100), hex: (000000-ffffff), --
                    ahex: (00000000-ffffffff) }

val(name, value, [context]) : set value

  'r':     red         (0-255)
  'g':     green       (0-255)
  'b':     blue        (0-255)
  'a':     alpha       (0-255)
  'h':     hue         (0-360)
  's':     saturation  (0-100)
  'v':     value       (0-100)
  'hex':   hex         (000000-ffffff)
  'ahex':  ahex        (00000000-ffffffff)
  
  ex. Usage

    val('r', (0-255)) || val('r', { r: (0-255) })
    val('h', (0-360)) || val('h', { h: (0-360) })
    val('hex', (000000-ffffff)) || val('hex', { hex: (000000-ffffff) })
    val('rg', { r: (0-255), g: (0-255) })
    val('rgba', { r: (-255), g: (0-255), b: (0-255), a: (0-255) })
    val(null, { r: (0-255), g: (0-255) })
    val('hvga'):  incorrect usage - cannot set hsv AND rgb as they will conflict</pre>
    </p><hr /><br />
    <h2>jPicker ColorMethod Utility Class</h2>
    <p>
      Static methods for altering and retrieving different color spaces.<br />
      <pre>
(jQuery).jPicker.ColorMethods.hexToRgba:
    function(hex)
    returns { r: (0-255), g: (0-255), b: (0-255), a: (0-255) }
    
(jQuery).jPicker.ColorMethods.validateHex:
    function(hex)
    returns new hex string
    
(jQuery).jPicker.ColorMethods.rgbaToHex:
    function({ r: (0-255), g: (0-255), b: (0-255), a: (0-255) })
    returns hex string
    
(jQuery).jPicker.ColorMethods.intToHex:
    function(number)
    returns hex string
    
(jQuery).jPicker.ColorMethods.hexToInt:
    function(hex)
    return integer

(jQuery).jPicker.ColorMethods.rgbToHsv:
    function({ r: (0-255), g: (0-255), b: (0-255) })
    returns { h: (0-360), s: (0-100), v: (0-100) }

(jQuery).jPicker.ColorMethods.hsvToRgb:
    function({ h: (0-360), s: (0-100), v: (0-100) })
    returns { r: (0-255), g: (0-255), b: (0-255) }
</pre>
    </p><hr /><br />
    <h2>Known Issues</h2>
    <ul>
      <li>
        <h3>Attaching multiple jPicker objects on a single page will slow performance.</h3>
        <ul>
          <li>jPicker creates a new instance of the picker for every element. Performance will suffer when binding dozens of instances.</li>
        </ul>
      </li>
    </ul><hr />
    <h2>Coming Soon</h2>
    <ul>
      <li>
        <ul>
          <li>Will consider supporting jQuery ThemeRoller CSS API for theming the UI if demand exists.</li>
        </ul>
      </li>
    </ul><hr />
    <h2>Planned For Future Release</h2>
    <ul>
      <li>
        Move the jPicker object to a single instance that all selection instances point to.
        <ul>
          <li>This will result in much faster operation and initialization for pages with multiple pickers.</li>
        </ul>
      </li>
      <li>Add activateCallback option for calling a callback function when the jPicker is activated or its binding is switched to a different picker element.</li>
    </ul><hr />
    <h2>Change Log</h2>
    <ul>
      <li>
        <h3>V1.1.5:</h3>
        <ul>
          <li>Corrected Color object constructor to allow setting of the "alpha" value as per the documentation which previously didn't work.</li>
          <li>Added support for translucency for quickList colors with checkered background - Only available if "alphaSupport" is enabled.</li>
          <li>Restricted default color to "alpha" of 255 if "alphaSupport" is disabled - It will now assign an explicit alpha of 255 when disabled.</li>
          <li>Added new setting variable "alphaPrecision" which indicates the number of decimal points to allow in the alpha percentage display - Now defaults to 0.</li>
        </ul>
      </li>
      <li>
        <h3>V1.1.4:</h3>
        <ul>
          <li>Changed "alpha" range from 0-100 to 0-255 to correct truncating and rounding errors caused by attempting to get an integer percentage that matches a hex value.</li>
          <li>"alpha" percentage display will now show up to 1 decimal point for more accurate representation of "alpha" value.</li>
          <li>Color object now accepts "alpha" values in a range of 0-255 and also returns the same when getting the "alpha" value. You will need to run ((alpha * 100) / 255) to retrieve a percentage value.</li>
          <li>Reworked the table layout and labels to remove the need for the label to reference the radio input box. This reduces injected code and removes the need to generate unique ids on the radio buttons.</li>
          <li>Transparent/invisible caret on NULL color is now corrected - uses the same caret color as a white color.</li>
          <li>Setting a binded input value of "" or no value attribute will now create a NULL color on initialization instead of the settings default color.</li>
          <li>Added a dynamic, invisible "iframe" behind a dialog picker in all browsers that fail jQuery.support.boxModel (currently IE <= 7 Quirks Mode). This prevents "select" box from showing through the picker.</li>
        </ul>
      </li>
      <li>
        <h3>V1.1.3:</h3>
        <ul>
          <li>Now adding popup color pickers to document.body instead of inline with the popup control. This corrects issues with the picker not showing beyond a relative container scope.</li>
          <li>No longer need to hide popup icon in Internet Explorer for picker elements lower in the DOM than the currently active one since the picker itself is attached to document.body (it is always higher in the DOM now).</li>
          <li>Popup pickers are now bring-to-front selectable. Clicking on the picker will bring it above all other pickers on the page instead of having to drag one out from underneath another.</li>
          <li>Corrected jPicker.List/setTimeout bug which allowed an instance to bind to the List in an order other than the order the initialization function was called.</li>
          <li>Added a updateInputColor option (default true) to allow for a binded input field that does not automatically update its background/text color.</li>
        </ul>
        <hr />
      </li>
      <li>
        <h3>V1.1.2:</h3>
        <ul>
          <li>Reworked the find methods and contexts for element searches. Now using ":first" instead of ".eq(0)" to take advantage of early out searches. Much faster initialization of the picker, on the order of 6 times.</li>
          <li>Now using setTimeout for calling visual updates. Dramatically improved marker dragging in all browsers. Reduces blocking as re-rendering is internal to the browser and independent of the other javascript still in progress.</li>
          <li>Marker updates can now cancel a previous valueChanged event when a new mouseMove event comes in. IE8 marker dragging is still slower, much over 5 times faster than it was.</li>
          <li>Reworked entire quickPick list creation. It now adds up source code and does a single "html" method instead of multiple "append" methods. This is a large part of the speed increase on initialization.</li>
          <li>The vast majority of all running scripts on both initialization and dragging is now occupied altering the style rules and finding elements (init only) instead of jPicker code.</li>
          <li>All methods previously called with global context now use the "call" method for using the context of the class running the method. "this" in a callback is now the DOM node (jQuery style) and jPicker instead of "window".</li>
          <li>Added "effects" section of window settings to allow different show/hide effects and durations.</li>
          <li>Removed change log and read me from the full source code to separate files (ChangeLog.txt and ReadMe.txt) and an HTML demonstration/documentation page (Example.txt).</li>
        </ul>
        <hr />
      </li>
      <li>
        <h3>V1.1.1:</h3>
        <ul>
          <li>Correct IE exception caused by attempting to set "#transparent" to CSS background-color.</li>
        </ul>
        <hr />
      </li>
      <li>
        <h3>V1.1.0:</h3>
        <ul>
          <li>Reworked nearly the entire plugin including the internal and external event model, bindings, DOM searches, classes, and overall presentation.</li>
          <li>The Color object now supports a changed event that you can bind to (or just bind to the picker events still included).</li>
          <li>Event order has been reversed, instead of an change event on the map/bar/text fields updating the display, they now update the Color object which then fires the events that update the display.</li>
          <li>alphaSupport re-implemented by request - default behavior is off.</li>
          <li>Hex code now only 6 characters again.</li>
          <li>Color object can now have its value changed from code, using the "val" method, and it will fire all events necessary to update the display.</li>
          <li>Removed all "get_*" methods from the color object, instead opting for a single "val" method for getting and setting, more in line with familiar jQuery methods.</li>
          <li>Better rendering for all IE versions in Quirks mode.</li>
        </ul>
        <hr />
      </li>
      <li>
        <h3>V1.0.13:</h3>
        <ul>
          <li>Updated transparency algorithm for red/green/blue color modes. The algorithm from John Dyers' color picker was close but incorrect. Bar colors are now pixel perfect with the new algorithm.</li>
          <li>Changed from using "background-position" on the color maps to an element of full height using the "top" attribute for image-map location using "overflow: hidden" to hide overdraw.</li>
          <li>IE7/8 ignores opacity on elements taller than 4096px. Image maps therefore no longer include a blank first map so the Bar is just under 4096. Blank is now accomplished by setting the "top" setting to below the map display.</li>
          <li>New colorBar picker image that does not draw outside of the element since the elements now hide overdraw.</li>
          <li>Added IE5.5/6 support for the picker. This is why it now uses maps of full height and the "top" attribute for map locations.</li>
          <li>Moved the images in the maps to 4 pixels apart from each other. IE7/8 change the first pixel of the bottom-border of 2px to partially transparent showing a portion of a different color map without this.</li>
        </ul>
        <hr />
      </li>
      <li>
        <h3>V1.0.12:</h3>
        <ul>
          <li>Added minified CSS file.</li>
          <li>Added IE7/8 Quirks Mode support.</li>
          <li>Added configurable string constants for all text and tooltips. You can now change the default values for different languages.</li>
          <li>Privatized the RGBA values in the Color object for better NULL handling. YOU MUST USE THE NEW GET FUNCTIONS TO ACCESS THE COLOR PROPERTIES.</li>
          <li>Better NULL color handling and an additional "No Color Selected" quick pick color.</li>
          <li>More consistent behavior across multiple versions of browsers.</li>
          <li>Added alpha response to the binded color picker icon.</li>
          <li>Removed "alphaSupport" variable. It is now always supported.</li>
        </ul>
        <hr />
      </li>
      <li>
        <h3>V1.0.11b:</h3>
        <ul>
          <li>Corrected NULL behavior in IE. jQuery was getting an exception when attempting to assign a backgroundColor style of '#'. Now assigns 'transparent' if color is NULL.</li>
          <li>Can now create new Color object WITH OR WITHOUT the '#' prefix.</li>
        </ul>
        <hr />
      </li>
      <li>
        <h3>V1.0.11:</h3>
        <ul>
          <li>Added ability for NULL colors (delete the hex value). Color will be returned as color.hex == ''. Can set the default color to an empty hex string as well.</li>
          <li>cancelCallback now returns the original color for use in programming responses.</li>
        </ul>
        <hr />
      </li>
      <li>
        <h3>V1.0.10:</h3>
        <ul>
          <li>Corrected table layout and tweaked display for more consisent presentation. Nice catch from Jonathan Pasquier.</li>
        </ul>
        <hr />
      </li>
      <li>
        <h3>V1.0.9:</h3>
        <ul>
          <li>Added optional title variable for each jPicker window.</li>
        </ul>
        <hr />
      </li>
      <li>
        <h3>V1.0.8:</h3>
        <ul>
          <li>Moved all images into a few sprites - now using backgroundPosition to change color maps and bars instead of changing the image - this should be faster to download and run.</li>
        </ul>
        <hr />
      </li>
      <li>
        <h3>V1.0.7:</h3>
        <ul>
          <li>RENAMED CSS FILE TO INCLUDE VERSION NUMBER!!! YOU MUST USE THIS VERSIONED CSS FILE!!! There will be no need to do your own CSS version number increments from now on.</li>
          <li>Added opacity feedback to color preview boxes.</li>
          <li>Removed reliance on "id" value of containing object. Subobjects are now found by class and container instead of id's. This drastically reduces injected code.</li>
          <li>Removed (jQuery).jPicker.getListElementById(id) function since "id" is no longer incorporated or required.</li>
        </ul>
        <hr />
      </li>
      <li>
        <h3>V1.0.6:</h3>
        <ul>
          <li>Corrected picker bugs introduced with 1.0.5.</li>
          <li>Removed alpha slider bar until activated - default behavior for alpha is now OFF.</li>
          <li>Corrected Color constructor bug not allowing values of 0 for initial value (it was evaluating false and missing the init code - Thanks Pavol).</li>
          <li>Removed title tags (tooltips) from color maps and bars - They get in the way in some browsers (e.g. IE - dragging marker does NOT prevent or hide the tooltip).</li>
          <li>THERE WERE CSS FILE CHANGES WITH THIS UPDATE!!! IF YOU USE NEVER-EXPIRE HEADERS, YOU WILL NEED TO INCREMENT YOUR CSS FILE VERSION NUMBER!!!</li>
        </ul>
        <hr />
      </li>
      <li>
        <h3>V1.0.5:</h3>
        <ul>
          <li>Added opacity support to picker and color/callback methods. New property "a" (alpha - range from 0-100) in all color objects now - defaults to 100% opaque. (Thank you Pavol)</li>
          <li>Added title attributes to input elements - gives short tooltip directions on what button or field does.</li>
          <li>Commit callback used to fire on control initialization (twice actually) - This has been corrected, it does not fire on initialization.</li>
          <li>THERE WERE CSS FILE CHANGES WITH THIS UPDATE!!! IF YOU USE NEVER-EXPIRE HEADERS, YOU WILL NEED TO INCREMENT YOUR CSS FILE VERSION NUMBER!!!</li>
        </ul>
        <hr />
      </li>
      <li>
        <h3>V1.0.4:</h3>
        <ul>
          <li>Added ability for smaller picker icon with expandable window on any DOM element (not just input).</li>
          <li>"draggable" property renamed to "expandable" and its scope increased to create small picker icon or large static picker.</li>
        </ul>
        <hr />
      </li>
      <li>
        <h3>V1.0.3</h3>
        <ul>
          <li>Added cancelCallback function for registering an external function when user clicks cancel button. (Thank you Jeff and Pavol)</li>
        </ul>
        <hr />
      </li>
      <li>
        <h3>V1.0.2</h3>
        <ul>
          <li>Random bug fixes - speed concerns.</li>
        </ul>
        <hr />
      </li>
      <li>
        <h3>V1.0.1</h3>
        <ul>
          <li>Corrected closure based memeory leak - there may be others?</li>
        </ul>
        <hr />
      </li>
      <li>
        <h3>V1.0.0</h3>
        <ul>
          <li>First Release.</li>
        </ul>
        <hr />
      </li>
    </ul>
  </div>
</body>
</html>