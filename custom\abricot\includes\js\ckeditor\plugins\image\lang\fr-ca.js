﻿/*
Copyright (c) 2003-2013, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'fr-ca', {
	alertUrl: 'Veuillez saisir l\'URL de l\'image',
	alt: 'Texte de remplacement',
	border: 'Bordure',
	btnUpload: 'Envoyer sur le serveur',
	button2Img: 'Do you want to transform the selected image button on a simple image?', // MISSING
	hSpace: 'Espacement horizontal',
	img2Button: 'Do you want to transform the selected image on a image button?', // MISSING
	infoTab: 'Informations sur l\'image',
	linkTab: 'Lien',
	lockRatio: 'Garder les proportions',
	menu: 'Propriétés de l\'image',
	resetSize: 'Taille originale',
	title: 'Propriétés de l\'image',
	titleButton: 'Propriétés du bouton image',
	upload: 'Télécharger',
	urlMissing: 'Image source URL is missing.', // MISSING
	vSpace: 'Espacement vertical',
	validateBorder: 'Border must be a whole number.', // MISSING
	validateHSpace: 'HSpace must be a whole number.', // MISSING
	validateVSpace: 'VSpace must be a whole number.' // MISSING
});
