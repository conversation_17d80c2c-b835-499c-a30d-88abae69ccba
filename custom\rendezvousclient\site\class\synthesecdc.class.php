<?php
/*
 *  \file       rendezvousclient/site/class/synthesecdc.class.php
 *  \ingroup    rendezvousclient
 *  \brief      Fichier de classe de la synthèse du cahier des charges
*/

Class SyntheseCDC
{
     /**
	 * @var string ID to identify managed object
	 */
	public $element = 'rendez_vous_synthese_cdc';

	/**
	 * @var string Name of table without prefix where object is stored
	 */
	public $table_element = 'rendez_vous_synthese_cdc';

    public $db;

	public $error;

    public $rowid;

    public $fk_projet;

    public $fk_source_cdc;

    public $version;

    public $date_creation;

    public $date_demo;

    public $date_livraison;

    public $nb_rdv_amont;

    public $nb_rdv_aval;

    public $nb_jours_formations;

    public $contexte_projet;

    public $objectifs_principaux;

    public $perimetre_projet;

    public $fonctionnalites_principales;

    public $contraintes_techniques;

    public $ressources_mobilisees;

    public $roles_responsabilites;

    public $delais_globaux;

    public $jalons_importants;

    public $criteres_acceptation;

    public $procedure_validation;

    public $budget_estimatif;

    public $modalites_paiement;

    /**
	 *	Constructor
	 *
	 *  @param		DoliDB		$db      Database handler
	 */
	public function __construct($db)
	{
		$this->db = $db;
	}

    /**
     * Fetch a synthese CDC record
     *
     * @param int $id ID of the synthese CDC
     * @return int 1 if OK, -1 if error
     */
    public function fetch($id)
    {
        // Check parameters
        if(empty($id)){
            return -1;
        }

        $sql = "SELECT rowid, fk_projet, fk_source_cdc, version, date_creation, date_demo, date_livraison, nb_rdv_amont, nb_rdv_aval, nb_jours_formations, contexte_projet, objectifs_principaux, perimetre_projet, fonctionnalites_principales, contraintes_techniques, ressources_mobilisees, roles_responsabilites, delais_globaux, jalons_importants, criteres_acceptation, procedure_validation, budget_estimatif, modalites_paiement";
        $sql .= " FROM ".MAIN_DB_PREFIX."rendez_vous_synthese_cdc";
        $sql .= " WHERE rowid = ".$id;

        $resql = $this->db->query($sql);
        if($resql){
            $obj = $this->db->fetch_object($resql);

            $this->rowid = $obj->rowid;
            $this->fk_projet = $obj->fk_projet;
            $this->fk_source_cdc = $obj->fk_source_cdc;
            $this->version = $obj->version;
            $this->date_creation = $obj->date_creation;
            $this->date_demo = $obj->date_demo;
            $this->date_livraison = $obj->date_livraison;
            $this->nb_rdv_amont = $obj->nb_rdv_amont;
            $this->nb_rdv_aval = $obj->nb_rdv_aval;
            $this->nb_jours_formations = $obj->nb_jours_formations;
            $this->contexte_projet = $obj->contexte_projet;
            $this->objectifs_principaux = $obj->objectifs_principaux;
            $this->perimetre_projet = $obj->perimetre_projet;
            $this->fonctionnalites_principales = $obj->fonctionnalites_principales;
            $this->contraintes_techniques = $obj->contraintes_techniques;
            $this->ressources_mobilisees = $obj->ressources_mobilisees;
            $this->roles_responsabilites = $obj->roles_responsabilites;
            $this->delais_globaux = $obj->delais_globaux;
            $this->jalons_importants = $obj->jalons_importants;
            $this->criteres_acceptation = $obj->criteres_acceptation;
            $this->procedure_validation = $obj->procedure_validation;
            $this->budget_estimatif = $obj->budget_estimatif;
            $this->modalites_paiement = $obj->modalites_paiement;

            return 1;
        }

        return -1;
    }

    /**
     * Create a new synthese CDC record
     *
     * @return int ID of created record if OK, -1 if error
     */
    public function create()
    {
        $sql = "INSERT INTO ".MAIN_DB_PREFIX."rendez_vous_synthese_cdc (";
        $sql .= "fk_projet, fk_source_cdc, version, date_creation, date_demo, date_livraison, nb_rdv_amont, nb_rdv_aval, nb_jours_formations, contexte_projet, objectifs_principaux, perimetre_projet, fonctionnalites_principales, contraintes_techniques, ressources_mobilisees, roles_responsabilites, delais_globaux, jalons_importants, criteres_acceptation, procedure_validation, budget_estimatif, modalites_paiement";
        $sql .= ") VALUES (";
        $sql .= $this->fk_projet.", ";
        $sql .= ($this->fk_source_cdc ? $this->fk_source_cdc : "NULL").", ";
        $sql .= "'".$this->db->escape($this->version)."', ";
        $sql .= "'".$this->db->idate($this->date_creation)."', ";
        $sql .= "'".$this->db->idate($this->date_demo)."', ";
        $sql .= "'".$this->db->idate($this->date_livraison)."', ";
        $sql .= "'".$this->db->escape($this->nb_rdv_amont)."', ";
        $sql .= "'".$this->db->escape($this->nb_rdv_aval)."', ";
        $sql .= "'".$this->db->escape($this->nb_jours_formations)."', ";
        $sql .= "'".$this->db->escape($this->contexte_projet)."', ";
        $sql .= "'".$this->db->escape($this->objectifs_principaux)."', ";
        $sql .= "'".$this->db->escape($this->perimetre_projet)."', ";
        $sql .= "'".$this->db->escape($this->fonctionnalites_principales)."', ";
        $sql .= "'".$this->db->escape($this->contraintes_techniques)."', ";
        $sql .= "'".$this->db->escape($this->ressources_mobilisees)."', ";
        $sql .= "'".$this->db->escape($this->roles_responsabilites)."', ";
        $sql .= "'".$this->db->escape($this->delais_globaux)."', ";
        $sql .= "'".$this->db->escape($this->jalons_importants)."', ";
        $sql .= "'".$this->db->escape($this->criteres_acceptation)."', ";
        $sql .= "'".$this->db->escape($this->procedure_validation)."', ";
        $sql .= "'".$this->db->escape($this->budget_estimatif)."', ";
        $sql .= "'".$this->db->escape($this->modalites_paiement)."'";
        $sql .= ")";

        $resql = $this->db->query($sql);
        if($resql){
            $this->rowid = $this->db->last_insert_id(MAIN_DB_PREFIX."rendez_vous_synthese_cdc");
            return $this->rowid;
        }

        return -1;
    }

    /**
     * Update a synthese CDC record
     *
     * @return int 1 if OK, -1 if error
     */
    public function update()
    {
        $sql = "UPDATE ".MAIN_DB_PREFIX."rendez_vous_synthese_cdc SET ";
        $sql .= "fk_projet = ".$this->fk_projet.", ";
        $sql .= "fk_source_cdc = ".($this->fk_source_cdc ? $this->fk_source_cdc : "NULL").", ";
        $sql .= "version = '".$this->db->escape($this->version)."', ";
        $sql .= "date_creation = '".$this->db->idate($this->date_creation)."', ";
        $sql .= "date_demo = '".$this->db->idate($this->date_demo)."', ";
        $sql .= "date_livraison = '".$this->db->idate($this->date_livraison)."', ";
        $sql .= "nb_rdv_amont = '".$this->db->escape($this->nb_rdv_amont)."', ";
        $sql .= "nb_rdv_aval = '".$this->db->escape($this->nb_rdv_aval)."', ";
        $sql .= "nb_jours_formations = '".$this->db->escape($this->nb_jours_formations)."', ";
        $sql .= "contexte_projet = '".$this->db->escape($this->contexte_projet)."', ";
        $sql .= "objectifs_principaux = '".$this->db->escape($this->objectifs_principaux)."', ";
        $sql .= "perimetre_projet = '".$this->db->escape($this->perimetre_projet)."', ";
        $sql .= "fonctionnalites_principales = '".$this->db->escape($this->fonctionnalites_principales)."', ";
        $sql .= "contraintes_techniques = '".$this->db->escape($this->contraintes_techniques)."', ";
        $sql .= "ressources_mobilisees = '".$this->db->escape($this->ressources_mobilisees)."', ";
        $sql .= "roles_responsabilites = '".$this->db->escape($this->roles_responsabilites)."', ";
        $sql .= "delais_globaux = '".$this->db->escape($this->delais_globaux)."', ";
        $sql .= "jalons_importants = '".$this->db->escape($this->jalons_importants)."', ";
        $sql .= "criteres_acceptation = '".$this->db->escape($this->criteres_acceptation)."', ";
        $sql .= "procedure_validation = '".$this->db->escape($this->procedure_validation)."', ";
        $sql .= "budget_estimatif = '".$this->db->escape($this->budget_estimatif)."', ";
        $sql .= "modalites_paiement = '".$this->db->escape($this->modalites_paiement)."'";
        $sql .= " WHERE rowid = ".$this->rowid;

        $resql = $this->db->query($sql);
        if($resql){
            return 1;
        }

        return -1;
    }

    /**
     * Get the URL for the synthese CDC
     *
     * @param int $id ID of the synthese CDC
     * @return string URL of the synthese CDC
     */
    public function getNomUrl($id)
    {
        // Check parameters
        if(empty($id)){
            return -1;
        }

        $sql = "SELECT rowid, version";
        $sql .= " FROM ".MAIN_DB_PREFIX."rendez_vous_synthese_cdc";
        $sql .= " WHERE rowid = ".$id;

        $resql = $this->db->query($sql);
        if($resql){
            $obj = $this->db->fetch_object($resql);

            return '<a href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/synthesecdc.php?id='.$obj->rowid.'">'.$obj->version.'</a>';
        }else{
            return -1;
        }
    }

    /**
     * Extract data from a cahier des charges to populate the synthese CDC
     *
     * @param int $cdc_id ID of the cahier des charges
     * @return int 1 if OK, -1 if error
     */
    public function extractFromCDC($cdc_id)
    {
        // TODO: Implement this method to extract data from cahier des charges
        return 1;
    }

    /**
     * Récupère les fonctionnalités principales à partir des modules sélectionnés pour un projet
     *
     * @param int $fk_projet ID du projet
     * @return string Texte des fonctionnalités
     */
    public function getFonctionnalitesFromModules($fk_projet)
    {
        $fonctionnalites = "";

        // Récupérer tous les sites du projet
        $sql = "SELECT rowid FROM ".MAIN_DB_PREFIX."rendez_vous_site WHERE fk_projet = ".$fk_projet;
        $resql = $this->db->query($sql);

        if ($resql) {
            while ($obj = $this->db->fetch_object($resql)) {
                $site_id = $obj->rowid;

                // Récupérer les modules sélectionnés pour ce site
                $sql2 = "SELECT rsm.fk_module, acm.libelle, acm.synthese_cdc";
                $sql2 .= " FROM ".MAIN_DB_PREFIX."rendez_vous_site_module rsm";
                $sql2 .= " INNER JOIN ".MAIN_DB_PREFIX."avimm_constante_module acm ON acm.rowid = rsm.fk_module";
                $sql2 .= " WHERE rsm.fk_site = ".$site_id." AND rsm.checked = 1";
                $sql2 .= " AND acm.synthese_cdc IS NOT NULL AND acm.synthese_cdc != ''";

                $resql2 = $this->db->query($sql2);
                if ($resql2) {
                    while ($obj2 = $this->db->fetch_object($resql2)) {
                        if (!empty($obj2->synthese_cdc)) {
                            $fonctionnalites .= "<h4>".$obj2->libelle."</h4>\n";
                            $fonctionnalites .= $obj2->synthese_cdc."\n\n";
                        }
                    }
                }
            }
        }

        return $fonctionnalites;
    }

    /**
     * Récupère les contraintes techniques à partir des logiciels sélectionnés pour un projet
     *
     * @param int $fk_projet ID du projet
     * @return string Texte des contraintes
     */
    public function getContraintesFromLogiciels($fk_projet)
    {
        $contraintes = "";

        // Récupérer tous les sites du projet
        $sql = "SELECT rowid, fk_logiciel FROM ".MAIN_DB_PREFIX."rendez_vous_site WHERE fk_projet = ".$fk_projet;
        $resql = $this->db->query($sql);

        if ($resql) {
            $logiciels_traites = array();

            while ($obj = $this->db->fetch_object($resql)) {
                if ($obj->fk_logiciel && !in_array($obj->fk_logiciel, $logiciels_traites)) {
                    // Récupérer les informations du logiciel
                    $sql2 = "SELECT libelle, cahier_des_charges FROM ".MAIN_DB_PREFIX."avimm_constante_logiciel";
                    $sql2 .= " WHERE rowid = ".$obj->fk_logiciel;
                    $sql2 .= " AND cahier_des_charges IS NOT NULL AND cahier_des_charges != ''";

                    $resql2 = $this->db->query($sql2);
                    if ($resql2 && $this->db->num_rows($resql2) > 0) {
                        $obj2 = $this->db->fetch_object($resql2);
                        if (!empty($obj2->cahier_des_charges)) {
                            $contraintes .= "<h4>".$obj2->libelle."</h4>\n";
                            $contraintes .= $obj2->cahier_des_charges."\n\n";
                        }
                    }

                    $logiciels_traites[] = $obj->fk_logiciel;
                }
            }
        }

        return $contraintes;
    }

    /**
     * Récupère les informations du tiers (client) à partir du projet
     *
     * @param int $fk_projet ID du projet
     * @return array Informations du tiers
     */
    public function getTiersInfoFromProject($fk_projet)
    {
        $tiers_info = array();

        $sql = "SELECT s.rowid, s.nom, s.phone, s.email";
        $sql .= " FROM ".MAIN_DB_PREFIX."societe s";
        $sql .= " INNER JOIN ".MAIN_DB_PREFIX."projet p ON p.fk_soc = s.rowid";
        $sql .= " WHERE p.rowid = ".$fk_projet;

        $resql = $this->db->query($sql);
        if ($resql && $this->db->num_rows($resql) > 0) {
            $obj = $this->db->fetch_object($resql);
            $tiers_info = array(
                'rowid' => $obj->rowid,
                'nom' => $obj->nom,
                'phone' => $obj->phone,
                'email' => $obj->email
            );
        }

        return $tiers_info;
    }
}
