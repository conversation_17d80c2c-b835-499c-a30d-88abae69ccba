﻿/*
Copyright (c) 2003-2013, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'image', 'pl', {
	alertUrl: 'Podaj adres obrazka.',
	alt: 'Tekst zastępczy',
	border: 'Obramowanie',
	btnUpload: 'Wyślij',
	button2Img: '<PERSON><PERSON> chcesz przekonwertować zaznaczony przycisk graficzny do zwykłego obrazka?',
	hSpace: 'Odstęp poziomy',
	img2Button: '<PERSON><PERSON> ch<PERSON><PERSON> przekonwertować zaznaczony obrazek do przycisku graficznego?',
	infoTab: 'Informacje o obrazku',
	linkTab: 'Hiperłącze',
	lockRatio: 'Zablokuj proporcje',
	menu: 'Właściwości obrazka',
	resetSize: 'Przy<PERSON>r<PERSON>ć rozmiar',
	title: 'W<PERSON>ściwości obrazka',
	titleButton: 'W<PERSON>ściwości przycisku graficznego',
	upload: 'Wyślij',
	urlMissing: 'Podaj adres URL obrazka.',
	vSpace: 'Odstęp pionowy',
	validateBorder: 'Wartość obramowania musi być liczbą całkowitą.',
	validateHSpace: 'Wartość odstępu poziomego musi być liczbą całkowitą.',
	validateVSpace: 'Wartość odstępu pionowego musi być liczbą całkowitą.'
});
