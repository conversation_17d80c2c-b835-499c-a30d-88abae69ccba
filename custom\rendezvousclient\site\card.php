<?php
/**
 *	\file       /rendezvousclient/site/card.php
 *	\ingroup    rendezvousclient
 *	\brief      fiche site
 */

require_once '../../../main.inc.php';

// Fix pour les propriétés manquantes de l'objet $user
if (!isset($user->projet)) {
    $user->projet = new stdClass();
}

require_once DOL_DOCUMENT_ROOT.'/societe/class/societe.class.php';
require_once DOL_DOCUMENT_ROOT.'/projet/class/project.class.php';
dol_include_once('/rendezvousclient/lib/rendezvousclient.lib.php');
dol_include_once('/rendezvousclient/site/class/site.class.php');
require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.formother.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/doleditor.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/files.lib.php';

// Load translation files required by the page
$langs->loadLangs(array('rendezvousclient', 'users', 'companies'));

$id = GETPOST('id', 'int');
$action = GETPOST('action', 'az09');
$cancel	= GETPOST('cancel', 'alphanohtml');
$backtopage = GETPOST('backtopage', 'alpha');
$help_url = '';


$site = new Site($db);
if($id > 0){
    $site->fetch($id);
}

$projectstatic = new Project($db);
if($site->fk_projet > 0){
    $projectstatic->fetch($site->fk_projet);
}

$societe = new Societe($db);
if($projectstatic->socid > 0){
    $societe->fetch($projectstatic->socid);
}


/*
 * Actions
*/

if($action == 'add' && GETPOST('save') == $langs->trans('Save')){
    $error = 0;

    // Récupération des données du formulaire
    $site->fk_projet = GETPOST('fk_projet', 'int');
    $site->nom = GETPOST('nom', 'alphanohtml');
    $site->type = GETPOST('type', 'alphanohtml');
    $site->description = GETPOST('description', 'restricthtml');
    $site->nombre_utilisateur = GETPOST('nb_user', 'int');
    $site->fk_logiciel = GETPOST('fk_logiciel', 'int');
    $site->autre = GETPOST('autre', 'alphanohtml');
    $site->hebergement = GETPOST('hebergement', 'restricthtml');

    // Date
    if (GETPOST('date')) {
        $site->date = dol_mktime(GETPOST('datehour', 'int'), GETPOST('datemin', 'int'), 0, GETPOST('datemonth', 'int'), GETPOST('dateday', 'int'), GETPOST('dateyear', 'int'));
    }

    // Validation
    if (empty($site->fk_projet)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("Project")), null, 'errors');
        $error++;
    }
    if (empty($site->nom)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("NomSite")), null, 'errors');
        $error++;
    }
    if (empty($site->type)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("TypeSite")), null, 'errors');
        $error++;
    }
    if (empty($site->fk_logiciel)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("LogicielPourSite")), null, 'errors');
        $error++;
    }

    if (!$error) {
        $result = $site->create();
        if ($result > 0) {
            // Sauvegarder les utilisateurs
            $type_users = GETPOST('type_user', 'array:alphanohtml');
            $descriptif_users = GETPOST('descriptif_user', 'array:restricthtml');

            if (is_array($type_users) && count($type_users) > 0) {
                for ($i = 0; $i < count($type_users); $i++) {
                    if (!empty($type_users[$i])) {
                        $site->createUtilisateur($type_users[$i], $descriptif_users[$i] ?? '');
                    }
                }
            }

            setEventMessages($langs->trans("RecordSaved"), null, 'mesgs');
            header("Location: ".DOL_URL_ROOT."/custom/rendezvousclient/site/card.php?id=".$result);
            exit;
        } else {
            setEventMessages($site->error, null, 'errors');
            $action = 'create';
        }
    } else {
        $action = 'create';
    }
}

if($action == 'update' && GETPOST('save') == $langs->trans('Save')){
    $error = 0;

    // Récupération des données du formulaire
    $site->nom = GETPOST('nom', 'alphanohtml');
    $site->type = GETPOST('type', 'alphanohtml');
    $site->description = GETPOST('description', 'restricthtml');
    $site->nombre_utilisateur = GETPOST('nb_user', 'int');
    $site->fk_logiciel = GETPOST('fk_logiciel', 'int');
    $site->autre = GETPOST('autre', 'alphanohtml');
    $site->hebergement = GETPOST('hebergement', 'restricthtml');

    // Date
    if (GETPOST('date')) {
        $site->date = dol_mktime(GETPOST('datehour', 'int'), GETPOST('datemin', 'int'), 0, GETPOST('datemonth', 'int'), GETPOST('dateday', 'int'), GETPOST('dateyear', 'int'));
    }

    // Validation
    if (empty($site->nom)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("NomSite")), null, 'errors');
        $error++;
    }
    if (empty($site->type)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("TypeSite")), null, 'errors');
        $error++;
    }
    if (empty($site->fk_logiciel)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("LogicielPourSite")), null, 'errors');
        $error++;
    }

    if (!$error) {
        $result = $site->update();
        if ($result > 0) {
            // Mettre à jour les utilisateurs
            $line_utilisateurs = GETPOST('line_utilisateur', 'array:int');
            $type_users = GETPOST('type_user', 'array:alphanohtml');
            $descriptif_users = GETPOST('descriptif_user', 'array:restricthtml');

            // Supprimer les anciens utilisateurs
            $site->deleteAllUtilisateurs();

            // Recréer les utilisateurs
            if (is_array($type_users) && count($type_users) > 0) {
                for ($i = 0; $i < count($type_users); $i++) {
                    if (!empty($type_users[$i])) {
                        $site->createUtilisateur($type_users[$i], $descriptif_users[$i] ?? '');
                    }
                }
            }

            setEventMessages($langs->trans("RecordSaved"), null, 'mesgs');
            header("Location: ".DOL_URL_ROOT."/custom/rendezvousclient/site/card.php?id=".$id);
            exit;
        } else {
            setEventMessages($site->error, null, 'errors');
            $action = 'edit';
        }
    } else {
        $action = 'edit';
    }
}



/*
 * View
*/

$form = new Form($db);
$formother = new FormOther($db);

$title = $langs->trans('Site');

llxHeader('', $title, $help_url);

if($action == 'create'){
    /*
     *  Creation
    */

    if(GETPOST('socid', 'int')){
        $societe->fetch(GETPOST('socid', 'int'));
    }

    $linkback = "";
    print load_fiche_titre($langs->trans("NewSite"), $linkback, 'generic');

    print '<form enctype="multipart/form-data" action="'.$_SERVER["PHP_SELF"].'" method="post" name="formsite" autocomplete="off">'; // Chrome ignor autocomplete
    print '<input type="hidden" name="action" value="add">';
    print '<input type="hidden" name="token" value="'.newToken().'">';
    // print '<input type="hidden" name="backtopage" value="'.$backtopage.'">';
    print dol_get_fiche_head(null, 'card', '', 0, '');

    print '<table class="border centpercent">';

    // Thirdparty
	print '<tr>';
	print '<td class="fieldrequired">'.$langs->trans('Customer').'</td>';
	if ($societe->id > 0) {
		if(!empty($conf->global->MAIN_AVIMM_CANCHANGESOC)){
			print '<td class="valuefieldcreate">';
			print img_picto('', 'company').$form->select_company($societe->id, 'socid', '', 'SelectThirdParty', 0, 0, null, 0, 'minwidth300 maxwidth500 widthcentpercentminusxx');
			// reload page to retrieve customer informations
			if (empty($conf->global->RELOAD_PAGE_ON_CUSTOMER_CHANGE_DISABLED)) {
				print '<script type="text/javascript">
				$(document).ready(function() {
					$("#socid").change(function() {
						console.log("We have changed the company - Reload page");
						var socid = $(this).val();
						// reload page
						$("input[name=action]").val("create");
						$("form[name=formsite]").submit();
					});
				});
				</script>';
			}
			print '</td>';
		}else{
			print '<td>';
			print $societe->getNomUrl(1, 'customer');
			print '<input type="hidden" name="socid" value="'.$societe->id.'">';
			print '</td>';
		}
	} else {
		print '<td>';
		print img_picto('', 'company').$form->select_company('', 'socid', '', 'SelectThirdParty', 0, 0, null, 0, 'minwidth175 maxwidth500 widthcentpercentminusxx');
		// reload page to retrieve customer informations
		if (empty($conf->global->RELOAD_PAGE_ON_CUSTOMER_CHANGE_DISABLED)) {
			print '<script type="text/javascript">
			$(document).ready(function() {
				$("#socid").change(function() {
					console.log("We have changed the company - Reload page");
					var socid = $(this).val();
					// reload page
					$("input[name=action]").val("create");
					$("form[name=formsite]").submit();
				});
			});
			</script>';
		}
		print ' <a href="'.DOL_URL_ROOT.'/societe/card.php?action=create&backtopage='.urlencode($_SERVER["PHP_SELF"].'?action=create').'"><span class="fa fa-plus-circle valignmiddle paddingleft" title="'.$langs->trans("AddThirdParty").'"></span></a>';
		print '</td>';
	}
	print '</tr>';

    if($societe->id){
        // projet
        $sql = "SELECT rowid, ref, title";
        $sql .= " FROM ".MAIN_DB_PREFIX."projet";
        $sql .= " WHERE fk_soc = ".$societe->id;
        $resql = $db->query($sql);

        print '<tr>';
        print '<td class="titlefieldcreate">'.$langs->trans('Project').'</td>';
        if($resql && $db->num_rows($resql) > 0){
            print '<td>';
            $arrayprojet = array();
            while($obj = $db->fetch_object($resql)){
                $arrayprojet[$obj->rowid] = $obj->ref.' - '.$obj->title;
            }
            print $form->selectarray('fk_projet', $arrayprojet, (GETPOST('fk_projet', 'int') ? GETPOST('fk_projet', 'int') : ''), 1);
            print '</td>';
        }else{
            print '<td>Aucun projet pour ce client';
            print ' <a href="'.DOL_URL_ROOT.'/projet/card.php?action=create&socid='.$societe->id.'&backtopage='.urlencode($_SERVER["PHP_SELF"].'?action=create').'"><span class="fa fa-plus-circle valignmiddle paddingleft" title="'.$langs->trans("AddThirdParty").'"></span></a>';
            print '</td>';
        }
        print '</tr>';
    }

    // date
    $date = '';
    if(GETPOST('date')){
        $date = dol_mktime(GETPOST('datehour', 'int'), GETPOST('datemin', 'int'), 0, GETPOST('datemonth', 'int'), GETPOST('dateday', 'int'), GETPOST('dateyear', 'int'));
    }
    print '<tr>';
    print '<td class="fieldrequired stitlefieldcreate">'.$langs->trans("Date").'</td>';
    print '<td>'.$form->selectDate($date, 'date', 1, 1, '', "formsite", 1, 0, 0, '', '', '', '', 1, '', '', 'auto').'</td>';
    print '</tr>';

    // nom du site
    print '<tr>';
    print '<td class="titlefieldcreate fieldrequired">'.$langs->trans("NomSite").'</td>';
    print '<td><input type="text" size="50" maxlength="255" name="nom" value="'.(GETPOST('nom', 'alpha') ? GETPOST('nom', 'alpha') : '').'"></td>';
    print '</tr>';

    // type de site
    print '<tr>';
    print '<td class="titlefieldcreate fieldrequired">'.$langs->trans("TypeSite").'</td>';
    print '<td><input type="text" size="50" maxlength="255" name="type" value="'.(GETPOST('type', 'alpha') ? GETPOST('type', 'alpha') : '').'"></td>';
    print '</tr>';

    // description
	print '<tr>';
	print '<td class="titlefieldcreate">'.$langs->trans('Description').'</td>';
	print '<td>';
    $description = '';
    if(GETPOST('description', 'restricthtml')){
        $description = GETPOST('description', 'restricthtml');
    }
	$doleditor = new DolEditor('description', $description, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    // nombre utilisateur
	print '<tr>';
	print '<td class="titlefieldcreate">'.$langs->trans('NbUser').'</td>';
	print '<td><input type="number" min="0" id="nb_user" name="nb_user" value="'.(GETPOST('nb_user', 'int') ? GETPOST('nb_user', 'int') : 0).'"></td>';
    print '<script type="text/javascript">
    $(document).ready(function(){
        $("#nb_user").on("click", function () {
            $(this).select();
        });
        $("#nb_user").on("change", function(){
            var rowCount = $("#usertable tr").length;
            var nbuser = $(this).val();

            // si moins d utilisateur que de ligne, supprime les lignes en trop
            if(nbuser < rowCount){
                // si les lignes dont l index est superieur au nombre d utilisateur
                $("#usertable tr:gt("+nbuser+")").remove();
            }

            var c = rowCount;
            while(c <= nbuser){
                $("#usertable > tbody:last-child").append("<tr><td><input type=\"text\" name=\"type_user[]\" placeholder=\"Type utilisateur "+c+"\"></td><td><textarea type=\"text\" name=\"descriptif_user[]\" placeholder=\"Descriptif utilisateur "+c+"\" rows=\"4\" cols=\"60\"></textarea></td></tr>");

                c = c + 1;
            }
            if(nbuser > 0){
                $("#typedescruser").attr("hidden", false);
            }else{
                $("#typedescruser").attr("hidden", true);
            }
        });
    });
    </script>';
    // print '<tr><td><input type="text" name="type_user[]" placeholder="type utilisateur 1"></td><td><textarea type="text" name="descriptif_user[]" placeholder="descriptif utilisateur 1" rows="4" cols="60"></textarea></td></tr>';
    print '</tr>';

    // type & descriptif utilisateur
    // cacher par defaut, parce qu'il y a 0 utilisateur dans nombre utilisateur par defaut
    print '<tr id="typedescruser" hidden>';
    print '<td class="titlefieldcreate" style="vertical-align: top;">'.$langs->trans('TypeDescriptifUser').'</td>';
    print '<td>';

    print '<table id="usertable" class="border">';
    print '<tr><td>'.$langs->trans('Type').'</td><td>'.$langs->trans('Descriptif').'</td></tr>';
    print '</table>';

    print '</td>';
    print '</tr>';

    // Logiciel
    $listlogiciel = $site->getArrayLogiciel();
    print '<tr>';
    print '<td class="titlefieldcreate fieldrequired">'.$langs->trans("LogicielPourSite").'</td>';
    print '<td>'.$form->selectarray('fk_logiciel', $listlogiciel, (GETPOST('fk_logiciel', 'int') ? GETPOST('fk_logiciel', 'int') : ''), 0, 0, 0, '', 0, 0, 0, '', 'maxwidth125', 1);
    print '<script type="text/javascript">
    $(document).ready(function(){
        $("#fk_logiciel").on("change", function(){
            if($(this).val() == 4){
                $("#autre").attr("hidden",false);
            }else{
                $("#autre").attr("hidden",true);
            }
        });
    });
    </script>';
    print '<input type="text" size="50" maxlength="255" name="autre" id="autre" value="'.(GETPOST('autre', 'alphanohtml') ? GETPOST('autre', 'alphanohtml') : '').'" hidden>';
    print '</td>';
    print '</tr>';

    // hebergement
    print '<tr>';
	print '<td class="titlefieldcreate">'.$langs->trans('Hebergement').'</td>';
	print '<td>';
    $hebergement = '';
    if(GETPOST('hebergement', 'restricthtml')){
        $hebergement = GETPOST('hebergement', 'restricthtml');
    }
	$doleditor = new DolEditor('hebergement', $hebergement, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    print '</table>';

    print dol_get_fiche_end();

    print $form->buttonsSaveCancel("Save");

	print '</form>';

}elseif($action == 'edit'){
    /*
     *  Edition
    */

    $head = site_prepare_head($site);

    print dol_get_fiche_head($head, 'card', $langs->trans("Site"), -1, 'generic');

    if(GETPOST('socid', 'int')){
        $societe->fetch(GETPOST('socid', 'int'));
    }

    print '
    <div class="arearef heightref valignmiddle centpercent">
        <!-- Start banner content -->
        <div style="vertical-align: middle">
            <div class="pagination paginationref">
                <ul class="right">
                </ul>
            </div>
            <!-- <div class="statusref">
                <span class="badge  badge-status4 badge-status" title="Ouvert">Ouvert</span>
            </div> -->
            <!-- morehtmlleft -->
            <div class="inline-block floatleft">
                <!-- No photo to show -->
                <div class="floatleft inline-block valignmiddle divphotoref">
                    <div class="photoref">
                        <!-- <span class="fas fa-project-diagram  em088 infobox-project" style="" title="No photo"></span> -->
                        '.img_picto('', 'generic').'
                    </div>
                </div>
            </div>
            <div class="inline-block floatleft valignmiddle maxwidth750 marginbottomonly refid refidpadding">
                <div class="refidno">
                    ';
                    print $langs->trans('ThirdParty').' : '.$societe->getNomUrl(1, '');

                    print '<br/>'.$langs->trans('Phone').' : '.dol_print_phone($societe->phone, $societe->country_code, $contactid, $societe->id, 'AC_TEL', '&nbsp;', 'phone', $langs->trans("Phone"));

                    print '<br/>'.$langs->trans('Email').' : '.dol_print_email($societe->email, $societe->id, $societe->id, 'AC_EMAIL', 0, 0, 1);

                    print '<br/>'.$langs->trans('Project').' : '. $projectstatic->ref;
                    print '
                </div>
            </div>
        </div>
        <!-- End banner content -->
    </div>';

    print '<div class="underbanner clearboth"></div>';

    print '<form enctype="multipart/form-data" action="'.$_SERVER["PHP_SELF"].'" method="post" name="formsite" autocomplete="off">'; // Chrome ignor autocomplete
    print '<input type="hidden" name="action" value="update">';
    print '<input type="hidden" name="token" value="'.newToken().'">';
    print '<input type="hidden" name="id" value="'.$id.'">';

    print '<table class="border centpercent">';

	// Thirdparty
	print '<tr>';
	print '<td class="fieldrequired">'.$langs->trans('Customer').'</td>';
    print '<td>';
    print $societe->getNomUrl(1, 'customer');
    print '</td>';
	print '</tr>';

    // projet
    print '<tr>';
	print '<td class="fieldrequired">'.$langs->trans('Project').'</td>';
    print '<td>';
    print $projectstatic->getNomUrl(1);
    print '</td>';
	print '</tr>';

    // date
    $date = ($site->date ? strtotime($site->date) : '');
    if(GETPOST('date')){
        $date = dol_mktime(GETPOST('datehour', 'int'), GETPOST('datemin', 'int'), 0, GETPOST('datemonth', 'int'), GETPOST('dateday', 'int'), GETPOST('dateyear', 'int'));
    }
    print '<tr>';
    print '<td class="fieldrequired stitlefieldcreate">'.$langs->trans("Date").'</td>';
    print '<td>'.$form->selectDate($date, 'date', 1, 1, '', "formsite", 1, 0, 0, '', '', '', '', 1, '', '', 'auto').'</td>';
    print '</tr>';

    // nom du site
    print '<tr>';
    print '<td class="titlefieldcreate fieldrequired">'.$langs->trans("NomSite").'</td>';
    print '<td><input type="text" size="50" maxlength="255" name="nom" value="'.(GETPOST('nom', 'alpha') ? GETPOST('nom', 'alpha') : $site->nom).'"></td>';
    print '</tr>';

    // type de site
    print '<tr>';
    print '<td class="titlefieldcreate fieldrequired">'.$langs->trans("TypeSite").'</td>';
    print '<td><input type="text" size="50" maxlength="255" name="type" value="'.(GETPOST('type', 'alpha') ? GETPOST('type', 'alpha') : $site->type).'"></td>';
    print '</tr>';

    // description
	print '<tr>';
	print '<td class="titlefieldcreate">'.$langs->trans('Description').'</td>';
	print '<td>';
    $description = $site->description;
    if(GETPOST('description', 'restricthtml')){
        $description = GETPOST('description', 'restricthtml');
    }
	$doleditor = new DolEditor('description', $description, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    // nombre utilisateur
	print '<tr>';
	print '<td class="titlefieldcreate">'.$langs->trans('NbUser').'</td>';
	print '<td><input type="number" min="0" id="nb_user" name="nb_user" value="'.(GETPOST('nb_user', 'int') ? GETPOST('nb_user', 'int') : $site->nombre_utilisateur).'"></td>';
    print '<script type="text/javascript">
    $(document).ready(function(){
        $("#nb_user").on("click", function () {
            $(this).select();
        });
        $("#nb_user").on("change", function(){
            var rowCount = $("#usertable tr").length;
            var nbuser = $(this).val();

            // si moins d utilisateur que de ligne, supprime les lignes en trop
            if(nbuser < rowCount){
                // si les lignes dont l index est superieur au nombre d utilisateur
                $("#usertable tr:gt("+nbuser+")").remove();
            }

            var c = rowCount;
            while(c <= nbuser){
                $("#usertable > tbody:last-child").append("<tr><td><input type=\"text\" name=\"type_user[]\" placeholder=\"Type utilisateur "+c+"\"></td><td><textarea type=\"text\" name=\"descriptif_user[]\" placeholder=\"Descriptif utilisateur "+c+"\" rows=\"4\" cols=\"60\"></textarea></td></tr>");

                c = c + 1;
            }
            if(nbuser > 0){
                $("#typedescruser").attr("hidden", false);
            }else{
                $("#typedescruser").attr("hidden", true);
            }
        });
    });
    </script>';
    // print '<tr><td><input type="text" name="type_user[]" placeholder="type utilisateur 1"></td><td><textarea type="text" name="descriptif_user[]" placeholder="descriptif utilisateur 1" rows="4" cols="60"></textarea></td></tr>';
    print '</tr>';

    // type & descriptif utilisateur
    // cacher par defaut, parce qu'il y a 0 utilisateur dans nombre utilisateur par defaut
    print '<tr id="typedescruser" '.(count($site->lines_utilisateur) > 0 ? '' : 'hidden').'>';
    print '<td class="titlefieldcreate" style="vertical-align: top;">'.$langs->trans('TypeDescriptifUser').'</td>';
    print '<td>';

    print '<table id="usertable" class="border">';
    print '<tr><td>'.$langs->trans('Type').'</td><td>'.$langs->trans('Descriptif').'</td></tr>';
    if(count($site->lines_utilisateur) > 0){
        $c = 1;
        foreach($site->lines_utilisateur as $line){
            print '<tr><td><input type="hidden" name="line_utilisateur[]" value="'.$line->rowid.'"><input type="text" name="type_user[]" placeholder="Type utilisateur '.$c.'" value="'.$line->type.'"></td><td><textarea type="text" name="descriptif_user[]" placeholder="Descriptif utilisateur '.$c.'" rows="4" cols="60">'.($line->descriptif ? $line->descriptif : '').'</textarea></td></tr>';

            $c++;
        }
    }
    print '</table>';

    print '</td>';
    print '</tr>';

    // Logiciel
    $listlogiciel = $site->getArrayLogiciel();
    print '<tr>';
    print '<td class="titlefieldcreate fieldrequired">'.$langs->trans("LogicielPourSite").'</td>';
    print '<td>'.$form->selectarray('fk_logiciel', $listlogiciel, (GETPOST('fk_logiciel', 'int') ? GETPOST('fk_logiciel', 'int') : $site->fk_logiciel), 0, 0, 0, '', 0, 0, 0, '', 'maxwidth125', 1);
    print '<script type="text/javascript">
    $(document).ready(function(){
        $("#fk_logiciel").on("change", function(){
            if($(this).val() == 4){
                $("#autre").attr("hidden",false);
            }else{
                $("#autre").attr("hidden",true);
            }
        });
    });
    </script>';
    print '<input type="text" size="50" maxlength="255" name="autre" id="autre" value="'.(GETPOST('autre', 'alphanohtml') ? GETPOST('autre', 'alphanohtml') : $site->autre).'" '.($site->fk_logiciel == 4 ? '' : 'hidden').'>';
    print '</td>';
    print '</tr>';

    // hebergement
    print '<tr>';
	print '<td class="titlefieldcreate">'.$langs->trans('Hebergement').'</td>';
	print '<td>';
    $hebergement = $site->hebergement;
    if(GETPOST('hebergement', 'restricthtml')){
        $hebergement = GETPOST('hebergement', 'restricthtml');
    }
	$doleditor = new DolEditor('hebergement', $hebergement, '', 80, 'dolibarr_notes', 'In', 0, false, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_3, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

    print '</table>';

    print dol_get_fiche_end();

    print $form->buttonsSaveCancel("Save");

    print '</form>';


}else{
     /*
     *  View
    */

    $site->socid = $societe->id;

    $head = site_prepare_head($site);

    print dol_get_fiche_head($head, 'card', $langs->trans("Site"), -1, 'generic');

    print '
    <div class="arearef heightref valignmiddle centpercent">
        <!-- Start banner content -->
        <div style="vertical-align: middle">
            <div class="pagination paginationref">
                <ul class="right">
                    <li class="noborder litext clearbothonsmartphone">
                        <a href="'.DOL_URL_ROOT.'/custom/rendezvousclient/site/list.php">Retour liste</a>
                    </li>
                </ul>
            </div>
            <!-- morehtmlleft -->
            <div class="inline-block floatleft">
                <!-- No photo to show -->
                <div class="floatleft inline-block valignmiddle divphotoref">
                    <div class="photoref">
                        <!-- <span class="fas fa-project-diagram  em088 infobox-project" style="" title="No photo"></span> -->
                        '.img_picto('', 'generic').'
                    </div>
                </div>
            </div>
            <div class="inline-block floatleft valignmiddle maxwidth750 marginbottomonly refid refidpadding">
                <div class="refidno">
                    ';
                    print $langs->trans('ThirdParty').' : '.$societe->getNomUrl(1, '');

                    print '<br/>'.$langs->trans('Phone').' ';
                    if($action != 'editphone'){
                        print '<a class="editfielda" href="'.$_SERVER['PHP_SELF'].'?id='.$id.'&action=editphone">'.img_edit().'</a> : ';
                        print dol_print_phone($societe->phone, $societe->country_code, $contactid, $societe->id, 'AC_TEL', '&nbsp;', 'phone', $langs->trans("Phone"));
                    }else{
                        print '<form method="post" action="'.$_SERVER['PHP_SELF'].'?id='.$id.'">';
                        print '<input type="hidden" name="action" value="setphone">';
                        print '<input type="hidden" name="token" value="'.newToken().'">';
                        print '<input type="text" name="phone" value="'.$societe->phone.'">';
                        print '<input type="submit" class="button valignmiddle" value="'.$langs->trans("Modify").'">';
                        print '</form>';
                    }

                    print '<br/>'.$langs->trans('Email').' ';
                    if($action != 'editemail'){
                        print '<a class="editfielda" href="'.$_SERVER['PHP_SELF'].'?id='.$id.'&action=editemail">'.img_edit().'</a> : ';
                        print dol_print_email($societe->email, $societe->id, $societe->id, 'AC_EMAIL', 0, 0, 1);
                    }else{
                        print '<form method="post" action="'.$_SERVER['PHP_SELF'].'?id='.$id.'">';
                        print '<input type="hidden" name="action" value="setemail">';
                        print '<input type="hidden" name="token" value="'.newToken().'">';
                        print '<input type="text" name="email" value="'.$societe->email.'">';
                        print '<input type="submit" class="button valignmiddle" value="'.$langs->trans("Modify").'">';
                        print '</form>';
                    }

                    print '<br/>'.$langs->trans('Project').' ';
                    if($action != 'editprojet'){
                        print '<a class="editfielda" href="'.$_SERVER['PHP_SELF'].'?id='.$id.'&action=editprojet">'.img_edit().'</a> : ';
                        print $projectstatic->getNomUrl(1);
                    }else{
                        print '<form method="post" action="'.$_SERVER['PHP_SELF'].'?id='.$id.'">';
                        print '<input type="hidden" name="action" value="setprojet">';
                        print '<input type="hidden" name="token" value="'.newToken().'">';
                        print $form->selectProjects($projectstatic->id, 'fk_projet');
                        print '<input type="submit" class="button valignmiddle" value="'.$langs->trans("Modify").'">';
                        print '</form>';
                    }
                    print '
                </div>
            </div>
        </div>
        <!-- End banner content -->
    </div>';

    print '<div class="fichecenter">';

    print '<div class="fichehalfleft">';
    print '<div class="underbanner clearboth"></div>';

    print '<table class="border tableforfield centpercent">';

    // nombre de site
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("NbSite").'</td>';
    print '<td>'.$site->getNbSite().'</td>';
    print '</tr>';

    // nom du site
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("NomSite").'</td>';
    print '<td>'.$site->nom.'</td>';
    print '</tr>';

    // date
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("Date").'</td>';
    print '<td>'.date('d/m/Y H:i:s', strtotime($site->date)).'</td>';
    print '</tr>';

    // type de site
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("TypeSite").'</td>';
    print '<td>'.$site->type.'</td>';
    print '</tr>';

    // description
    print '<tr>';
	print '<td class="titlefield">'.$langs->trans('Description').'</td>';
    print '<td>'.dol_htmlentitiesbr($site->description).'</td>';
    print '</tr>';

    // nombre utilisateur
    print '<tr>';
	print '<td class="titlefield">'.$langs->trans('NbUser').'</td>';
    print '<td>'.$site->nombre_utilisateur.'</td>';
    print '</tr>';

    if(count($site->lines_utilisateur) > 0){
        print '<tr>';
        print '<td class="titlefield" style="vertical-align: top;">'.$langs->trans('TypeDescriptifUser').'</td>';

        print '<td>';
        print '<table class="border centpercent">';
        print '<tr><td style="color: black;">'.$langs->trans('Type').'</td><td>'.$langs->trans('Descriptif').'</td></tr>';
        $num = count($site->lines_utilisateur);
        $c = 1;

        foreach($site->lines_utilisateur as $site_u){
            if($c == $num){
                $style = 'border-bottom: none;';
            }
            print '<tr>';
            print '<td style="color: black;'.$style.'">'.$site_u->type.'</td>';
            print '<td style="'.$style.'">'.dol_htmlentitiesbr($site_u->descriptif).'</td>';
            print '</tr>';
            $c++;
        }
        print '</table>';
        print '</td>';

        print '</tr>';
    }

    // Logiciel
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans("LogicielPourSite").'</td>';
    if($site->fk_logiciel == 4){
        print '<td>Autre: '.$site->autre.'</td>';
    }else{
        print '<td>'.$site->getLibLogiciel($site->fk_logiciel).'</td>';
    }
    print '</tr>';

    // hebergement
    print '<tr>';
	print '<td class="titlefield">'.$langs->trans('Hebergement').'</td>';
    print '<td>'.dol_htmlentitiesbr($site->hebergement).'</td>';
    print '</tr>';


    print '</table>';

    print '</div>'; // fin div fichehalfleft

    print '<div class="fichehalfright">';
    print '<div class="underbanner clearboth"></div>';

    print 'Suivi site client <br/>';
    print '<table class="liste border centpercent">';

    print '<tr class="liste_titre">';
    print '<td style="width: 40%">'.$langs->trans("NomSite").'</td>';
    print '<td style="width: 40%">'.$langs->trans("TypeSite").'</td>';
    print '<td style="width: 20%">'.$langs->trans("Date").'</td>';
    print '</tr>';

    $site->getOtherSite();
    if(count($site->othersite) > 0){
        // compte le nombre de ligne affiché, si toujours 0 (dans le cas la societe n'a que 1 site qui est celui qui est deja afficher sur cette page, aucune ligne ne sera afficher)
        $ligne = 0;

        foreach($site->othersite as $line){
            if($line->rowid != $site->rowid){
                $ligne++;

	            print "<tr onclick=\"window.location='card.php?id=".$line->rowid."&save_lastsearch_values=1'\" class=\"oddeven\">";
                print '<td>'.$line->nom.'</td>';
                print '<td>'.$line->type.'</td>';
                print '<td>'.date('d/m/Y H:i:s', strtotime($line->date)).'</td>';
                print '</tr>';
            }
        }

        if($ligne == 0){
            print '<tr class="oddeven">';
            print '<td colspan="3">Aucun autre site</td>';
            print '</tr>';
        }
    }else{
        print '<tr class="oddeven">';
        print '<td colspan="3">Aucun autre site</td>';
        print '</tr>';
    }

    print '</table>';


    print '</div>'; // fin div fichehalfright

    print '</div>'; // fin div fichecenter

    print dol_get_fiche_end();


    print '<div class="clearboth"></div><br>';

    print '<div class="tabsAction">'."\n";

    print dolGetButtonAction('', $langs->trans('createcahiercharge'), 'default', $_SERVER["PHP_SELF"].'?id='.$site->rowid.'#'.newToken(), '', 1);

    print dolGetButtonAction('', $langs->trans('createdevis'), 'default', $_SERVER["PHP_SELF"].'?id='.$site->rowid.'#'.newToken(), '', 1);

    print dolGetButtonAction('', $langs->trans('Modify'), 'default', $_SERVER["PHP_SELF"].'?id='.$site->rowid.'&action=edit&token='.newToken(), '', 1);

    print dolGetButtonAction('', $langs->trans('Delete'), 'default', $_SERVER["PHP_SELF"].'?id='.$site->rowid.'#'.newToken(), '', 1);

    print dolGetButtonAction('', $langs->trans('SendMail'), 'default', $_SERVER["PHP_SELF"].'?id='.$site->rowid.'#'.newToken(), '', 1);

    print '</div>';
}


// End of page
llxFooter();
$db->close();
